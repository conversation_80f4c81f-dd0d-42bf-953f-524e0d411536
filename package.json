{"name": "accounting-system", "version": "1.0.0", "description": "برنامج محاسبة شامل لإدارة الحسابات والقيود والفواتير والتقارير المالية", "main": "index.html", "scripts": {"start": "python -m http.server 8080", "serve": "http-server -p 8080 -c-1", "dev": "live-server --port=8080 --entry-file=index.html", "build": "echo 'No build process needed for static files'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "format": "echo 'No formatting configured'"}, "keywords": ["accounting", "finance", "bookkeeping", "invoicing", "reports", "arabic", "محاسبة", "مالية", "فواتير"], "author": "Accounting System Developer", "license": "MIT", "homepage": ".", "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "files": ["*.html", "*.css", "*.js", "*.json", "*.md"]}