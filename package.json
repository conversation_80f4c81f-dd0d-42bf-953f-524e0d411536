{"name": "accounting-system", "version": "1.0.0", "description": "برنامج محاسبة شامل لإدارة الحسابات والقيود والفواتير والتقارير المالية", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "keywords": ["accounting", "finance", "bookkeeping", "invoicing", "reports", "arabic", "محاسبة", "مالية", "فواتير", "desktop", "electron"], "author": {"name": "Accounting System Developer", "email": "<EMAIL>"}, "license": "MIT", "homepage": ".", "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "dependencies": {"electron-store": "^8.1.0"}, "devDependencies": {"electron": "^22.3.27", "electron-builder": "^24.6.4"}, "engines": {"node": ">=14.0.0"}, "build": {"appId": "com.accounting.system", "productName": "برنامج المحاسبة", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!.git/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "برنامج المحاسبة"}}}