# دليل بناء تطبيق سطح المكتب

## متطلبات النظام

### البرامج المطلوبة:
- **Node.js** (الإصدار 14 أو أحدث)
- **npm** (يأتي مع Node.js)
- **Git** (اختياري)

### تحميل Node.js:
1. اذهب إلى [nodejs.org](https://nodejs.org)
2. حمل النسخة LTS (الموصى بها)
3. قم بتثبيتها واتبع التعليمات

## خطوات البناء

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. تشغيل التطبيق في وضع التطوير
```bash
npm start
```
أو
```bash
npm run dev
```

### 3. بناء التطبيق للتوزيع

#### بناء لجميع المنصات:
```bash
npm run build
```

#### بناء لنظام Windows فقط:
```bash
npm run build-win
```

#### بناء لنظام macOS فقط:
```bash
npm run build-mac
```

#### بناء لنظام Linux فقط:
```bash
npm run build-linux
```

### 4. إنشاء حزمة بدون تثبيت:
```bash
npm run pack
```

## ملفات الإخراج

بعد البناء، ستجد الملفات في مجلد `dist/`:

### Windows:
- `برنامج المحاسبة Setup 1.0.0.exe` - ملف التثبيت
- `برنامج المحاسبة 1.0.0.exe` - الملف التنفيذي المحمول

### macOS:
- `برنامج المحاسبة-1.0.0.dmg` - ملف التثبيت لنظام Mac

### Linux:
- `برنامج المحاسبة-1.0.0.AppImage` - ملف تنفيذي محمول لنظام Linux

## الميزات المضافة في إصدار سطح المكتب

### 1. قوائم التطبيق:
- **ملف**: جديد، فتح، حفظ، حفظ باسم، طباعة، خروج
- **تحرير**: تراجع، إعادة، قص، نسخ، لصق، تحديد الكل
- **عرض**: إعادة تحميل، تكبير/تصغير، تحكم في الزوم
- **أدوات**: أدوات المطور
- **مساعدة**: حول البرنامج

### 2. اختصارات لوحة المفاتيح:
- `Ctrl+N` - ملف جديد
- `Ctrl+O` - فتح ملف
- `Ctrl+S` - حفظ
- `Ctrl+Shift+S` - حفظ باسم
- `Ctrl+P` - طباعة
- `Ctrl+Q` - خروج
- `Ctrl+Z` - تراجع
- `Ctrl+Y` - إعادة
- `Ctrl+X` - قص
- `Ctrl+C` - نسخ
- `Ctrl+V` - لصق
- `Ctrl+A` - تحديد الكل
- `Ctrl+R` - إعادة تحميل
- `F11` - ملء الشاشة
- `Ctrl++` - تكبير
- `Ctrl+-` - تصغير
- `Ctrl+0` - الحجم الطبيعي
- `F12` - أدوات المطور

### 3. نوافذ حوار نظام التشغيل:
- نافذة فتح الملفات
- نافذة حفظ الملفات
- رسائل التأكيد والخطأ
- نافذة حول البرنامج

### 4. إدارة الملفات:
- حفظ وتحميل البيانات من/إلى ملفات JSON
- تصدير واستيراد البيانات بواجهة نظام التشغيل
- حفظ تلقائي للإعدادات

## استكشاف الأخطاء

### مشكلة: فشل في تثبيت التبعيات
**الحل:**
```bash
# مسح cache npm
npm cache clean --force

# حذف مجلد node_modules وإعادة التثبيت
rm -rf node_modules
npm install
```

### مشكلة: فشل في البناء على Windows
**الحل:**
- تأكد من تثبيت Visual Studio Build Tools
- أو ثبت windows-build-tools:
```bash
npm install --global windows-build-tools
```

### مشكلة: حجم الملف التنفيذي كبير
**الحل:**
- هذا طبيعي لتطبيقات Electron (حوالي 100-150 ميجابايت)
- يتضمن الملف محرك Chromium و Node.js

### مشكلة: التطبيق لا يعمل على أجهزة أخرى
**الحل:**
- تأكد من بناء التطبيق للمنصة الصحيحة
- تأكد من تثبيت Visual C++ Redistributable على Windows

## تخصيص التطبيق

### تغيير الأيقونة:
1. استبدل الملفات في مجلد `assets/`
2. تأكد من وجود الأيقونات بالصيغ المطلوبة:
   - `icon.ico` لنظام Windows
   - `icon.icns` لنظام macOS
   - `icon.png` لنظام Linux

### تغيير معلومات التطبيق:
عدل ملف `package.json`:
```json
{
  "name": "اسم-التطبيق",
  "version": "1.0.0",
  "description": "وصف التطبيق",
  "author": "اسم المطور"
}
```

### تغيير إعدادات البناء:
عدل قسم `build` في `package.json`:
```json
{
  "build": {
    "appId": "com.yourcompany.yourapp",
    "productName": "اسم التطبيق",
    "directories": {
      "output": "dist"
    }
  }
}
```

## الأمان

### الميزات الأمنية المطبقة:
- تعطيل `nodeIntegration`
- تفعيل `contextIsolation`
- استخدام `preload.js` للتواصل الآمن
- منع التنقل إلى مواقع خارجية
- فتح الروابط الخارجية في المتصفح الافتراضي

### نصائح أمنية:
- لا تعطل الميزات الأمنية
- تحديث Electron بانتظام
- فحص التبعيات للثغرات الأمنية:
```bash
npm audit
npm audit fix
```

## الدعم

### المتطلبات الدنيا للنظام:
- **Windows**: Windows 7 أو أحدث
- **macOS**: macOS 10.10 أو أحدث
- **Linux**: Ubuntu 12.04 أو أحدث

### حجم التطبيق:
- حوالي 100-150 ميجابايت (يتضمن محرك Chromium)

### الأداء:
- استهلاك ذاكرة: 50-100 ميجابايت
- سرعة بدء التشغيل: 2-5 ثوان

---

**ملاحظة**: هذا التطبيق مبني باستخدام Electron، مما يعني أنه يعمل على جميع أنظمة التشغيل الرئيسية بنفس الكود.
