const { contextBridge, ipcRenderer } = require('electron');

// تعريض API آمن للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ ملف
    saveFile: (filePath, data) => ipcRenderer.invoke('save-file', filePath, data),
    
    // تحميل ملف
    loadFile: (filePath) => ipcRenderer.invoke('load-file', filePath),
    
    // إظهار نافذة حفظ
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    
    // إظهار نافذة فتح
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    
    // إظهار رسالة
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    
    // الاستماع لأحداث القائمة
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-action', (event, action, data) => {
            callback(action, data);
        });
    },
    
    // إزالة مستمع الأحداث
    removeMenuListener: () => {
        ipcRenderer.removeAllListeners('menu-action');
    },
    
    // معلومات النظام
    platform: process.platform,
    
    // إصدار Electron
    versions: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron
    }
});

// إضافة مؤشر أن التطبيق يعمل في Electron
contextBridge.exposeInMainWorld('isElectron', true);

// إضافة وظائف مساعدة للتطبيق
contextBridge.exposeInMainWorld('desktopUtils', {
    // تصدير البيانات إلى ملف
    exportData: async (data) => {
        try {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'تصدير البيانات',
                defaultPath: `accounting_backup_${new Date().toISOString().split('T')[0]}.json`,
                filters: [
                    { name: 'ملفات البيانات', extensions: ['json'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled) {
                const saveResult = await ipcRenderer.invoke('save-file', result.filePath, JSON.stringify(data, null, 2));
                return { success: saveResult.success, filePath: result.filePath, error: saveResult.error };
            }
            
            return { success: false, canceled: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },
    
    // استيراد البيانات من ملف
    importData: async () => {
        try {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                title: 'استيراد البيانات',
                properties: ['openFile'],
                filters: [
                    { name: 'ملفات البيانات', extensions: ['json'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
                const loadResult = await ipcRenderer.invoke('load-file', result.filePaths[0]);
                if (loadResult.success) {
                    try {
                        const data = JSON.parse(loadResult.data);
                        return { success: true, data, filePath: result.filePaths[0] };
                    } catch (parseError) {
                        return { success: false, error: 'ملف البيانات غير صحيح' };
                    }
                }
                return { success: false, error: loadResult.error };
            }
            
            return { success: false, canceled: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },
    
    // إظهار رسالة تأكيد
    showConfirmDialog: async (message, title = 'تأكيد') => {
        const result = await ipcRenderer.invoke('show-message-box', {
            type: 'question',
            title: title,
            message: message,
            buttons: ['نعم', 'لا'],
            defaultId: 0,
            cancelId: 1
        });
        
        return result.response === 0;
    },
    
    // إظهار رسالة معلومات
    showInfoDialog: async (message, title = 'معلومات') => {
        await ipcRenderer.invoke('show-message-box', {
            type: 'info',
            title: title,
            message: message,
            buttons: ['موافق']
        });
    },
    
    // إظهار رسالة خطأ
    showErrorDialog: async (message, title = 'خطأ') => {
        await ipcRenderer.invoke('show-message-box', {
            type: 'error',
            title: title,
            message: message,
            buttons: ['موافق']
        });
    },
    
    // طباعة الصفحة الحالية
    printPage: () => {
        window.print();
    }
});

// إضافة مستمع لأحداث النافذة
window.addEventListener('DOMContentLoaded', () => {
    // إضافة مؤشر أن التطبيق يعمل في Electron
    document.body.classList.add('electron-app');
    
    // إضافة معلومات النظام
    const platformClass = `platform-${process.platform}`;
    document.body.classList.add(platformClass);
    
    // تحديث عنوان النافذة
    document.title = 'برنامج المحاسبة - إصدار سطح المكتب';
});

// منع السحب والإفلات للملفات (لتجنب التنقل غير المرغوب فيه)
document.addEventListener('dragover', (e) => {
    e.preventDefault();
    e.stopPropagation();
});

document.addEventListener('drop', (e) => {
    e.preventDefault();
    e.stopPropagation();
});
