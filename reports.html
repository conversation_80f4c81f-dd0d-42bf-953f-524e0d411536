<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المالية - برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link">القيود</a></li>
                <li><a href="invoices.html" class="nav-link">الفواتير</a></li>
                <li><a href="reports.html" class="nav-link active">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1>التقارير المالية</h1>
            <div class="report-actions">
                <button class="btn btn-primary" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
                <button class="btn btn-secondary" onclick="printReport()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </div>

        <!-- اختيار نوع التقرير -->
        <div class="report-selector">
            <div class="report-tabs">
                <button class="tab-btn active" data-report="balance-sheet">الميزانية العمومية</button>
                <button class="tab-btn" data-report="income-statement">قائمة الدخل</button>
                <button class="tab-btn" data-report="trial-balance">ميزان المراجعة</button>
                <button class="tab-btn" data-report="cash-flow">التدفق النقدي</button>
            </div>
        </div>

        <!-- فلاتر التاريخ -->
        <div class="date-range-selector">
            <div class="date-inputs">
                <label for="reportFromDate">من تاريخ:</label>
                <input type="date" id="reportFromDate">
                <label for="reportToDate">إلى تاريخ:</label>
                <input type="date" id="reportToDate">
                <button class="btn btn-primary" onclick="generateReport()">إنشاء التقرير</button>
            </div>
        </div>

        <!-- محتوى التقارير -->
        <div id="reportContent" class="report-content">
            <!-- الميزانية العمومية -->
            <div id="balance-sheet" class="report-section active">
                <h2>الميزانية العمومية</h2>
                <div class="balance-sheet-container">
                    <div class="assets-section">
                        <h3>الأصول</h3>
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>الحساب</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody id="assetsTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                            <tfoot>
                                <tr class="total-row">
                                    <td><strong>إجمالي الأصول</strong></td>
                                    <td><strong id="totalAssets">0 ر.س</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <div class="liabilities-equity-section">
                        <div class="liabilities-section">
                            <h3>الخصوم</h3>
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="liabilitiesTable">
                                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                </tbody>
                                <tfoot>
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong id="totalLiabilities">0 ر.س</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="equity-section">
                            <h3>حقوق الملكية</h3>
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="equityTable">
                                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                </tbody>
                                <tfoot>
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong id="totalEquity">0 ر.س</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الدخل -->
            <div id="income-statement" class="report-section">
                <h2>قائمة الدخل</h2>
                <div class="income-statement-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>البيان</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="section-header">
                                <td colspan="2"><strong>الإيرادات</strong></td>
                            </tr>
                            <tbody id="revenueTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                            <tr class="subtotal-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong id="totalRevenue">0 ر.س</strong></td>
                            </tr>
                            <tr class="section-header">
                                <td colspan="2"><strong>المصروفات</strong></td>
                            </tr>
                            <tbody id="expenseTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                            <tr class="subtotal-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong id="totalExpenses">0 ر.س</strong></td>
                            </tr>
                            <tr class="total-row">
                                <td><strong>صافي الربح/الخسارة</strong></td>
                                <td><strong id="netIncome">0 ر.س</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- ميزان المراجعة -->
            <div id="trial-balance" class="report-section">
                <h2>ميزان المراجعة</h2>
                <div class="trial-balance-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>رقم الحساب</th>
                                <th>اسم الحساب</th>
                                <th>المدين</th>
                                <th>الدائن</th>
                            </tr>
                        </thead>
                        <tbody id="trialBalanceTable">
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                        <tfoot>
                            <tr class="total-row">
                                <td colspan="2"><strong>الإجمالي</strong></td>
                                <td><strong id="totalDebitBalance">0 ر.س</strong></td>
                                <td><strong id="totalCreditBalance">0 ر.س</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <!-- التدفق النقدي -->
            <div id="cash-flow" class="report-section">
                <h2>قائمة التدفق النقدي</h2>
                <div class="cash-flow-container">
                    <div class="chart-container">
                        <canvas id="cashFlowChart"></canvas>
                    </div>
                    <div class="cash-flow-summary">
                        <div class="cash-flow-item">
                            <h4>التدفق النقدي من العمليات التشغيلية</h4>
                            <p id="operatingCashFlow">0 ر.س</p>
                        </div>
                        <div class="cash-flow-item">
                            <h4>التدفق النقدي من الأنشطة الاستثمارية</h4>
                            <p id="investingCashFlow">0 ر.س</p>
                        </div>
                        <div class="cash-flow-item">
                            <h4>التدفق النقدي من الأنشطة التمويلية</h4>
                            <p id="financingCashFlow">0 ر.س</p>
                        </div>
                        <div class="cash-flow-item total">
                            <h4>صافي التدفق النقدي</h4>
                            <p id="netCashFlow">0 ر.س</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="script.js"></script>
    <script>
        let currentReport = 'balance-sheet';
        let cashFlowChart = null;

        // تهيئة صفحة التقارير
        document.addEventListener('DOMContentLoaded', function() {
            loadReportsPage();
            setupReportsEventListeners();
        });

        function loadReportsPage() {
            // تعيين التواريخ الافتراضية
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('reportFromDate').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('reportToDate').value = today.toISOString().split('T')[0];
            
            generateReport();
        }

        function setupReportsEventListeners() {
            // تبديل التقارير
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    document.querySelectorAll('.report-section').forEach(s => s.classList.remove('active'));
                    
                    this.classList.add('active');
                    currentReport = this.dataset.report;
                    document.getElementById(currentReport).classList.add('active');
                    
                    generateReport();
                });
            });
        }

        function generateReport() {
            switch(currentReport) {
                case 'balance-sheet':
                    generateBalanceSheet();
                    break;
                case 'income-statement':
                    generateIncomeStatement();
                    break;
                case 'trial-balance':
                    generateTrialBalance();
                    break;
                case 'cash-flow':
                    generateCashFlow();
                    break;
            }
        }

        function generateBalanceSheet() {
            // الأصول
            const assets = accounts.filter(acc => acc.type === 'asset');
            const assetsTableBody = document.getElementById('assetsTable');
            let totalAssetsAmount = 0;

            assetsTableBody.innerHTML = assets.map(account => {
                totalAssetsAmount += account.balance;
                return `
                    <tr>
                        <td>${account.name}</td>
                        <td>${formatCurrency(account.balance)}</td>
                    </tr>
                `;
            }).join('');

            // الخصوم
            const liabilities = accounts.filter(acc => acc.type === 'liability');
            const liabilitiesTableBody = document.getElementById('liabilitiesTable');
            let totalLiabilitiesAmount = 0;

            liabilitiesTableBody.innerHTML = liabilities.map(account => {
                totalLiabilitiesAmount += account.balance;
                return `
                    <tr>
                        <td>${account.name}</td>
                        <td>${formatCurrency(account.balance)}</td>
                    </tr>
                `;
            }).join('');

            // حقوق الملكية
            const equity = accounts.filter(acc => acc.type === 'equity');
            const equityTableBody = document.getElementById('equityTable');
            let totalEquityAmount = 0;

            equityTableBody.innerHTML = equity.map(account => {
                totalEquityAmount += account.balance;
                return `
                    <tr>
                        <td>${account.name}</td>
                        <td>${formatCurrency(account.balance)}</td>
                    </tr>
                `;
            }).join('');

            // تحديث الإجماليات
            document.getElementById('totalAssets').textContent = formatCurrency(totalAssetsAmount);
            document.getElementById('totalLiabilities').textContent = formatCurrency(totalLiabilitiesAmount);
            document.getElementById('totalEquity').textContent = formatCurrency(totalEquityAmount);
        }

        function generateIncomeStatement() {
            // الإيرادات
            const revenues = accounts.filter(acc => acc.type === 'revenue');
            const revenueTableBody = document.getElementById('revenueTable');
            let totalRevenueAmount = 0;

            revenueTableBody.innerHTML = revenues.map(account => {
                totalRevenueAmount += account.balance;
                return `
                    <tr>
                        <td>${account.name}</td>
                        <td>${formatCurrency(account.balance)}</td>
                    </tr>
                `;
            }).join('');

            // المصروفات
            const expenses = accounts.filter(acc => acc.type === 'expense');
            const expenseTableBody = document.getElementById('expenseTable');
            let totalExpenseAmount = 0;

            expenseTableBody.innerHTML = expenses.map(account => {
                totalExpenseAmount += account.balance;
                return `
                    <tr>
                        <td>${account.name}</td>
                        <td>${formatCurrency(account.balance)}</td>
                    </tr>
                `;
            }).join('');

            const netIncomeAmount = totalRevenueAmount - totalExpenseAmount;

            // تحديث الإجماليات
            document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenueAmount);
            document.getElementById('totalExpenses').textContent = formatCurrency(totalExpenseAmount);
            document.getElementById('netIncome').textContent = formatCurrency(netIncomeAmount);
            document.getElementById('netIncome').style.color = netIncomeAmount >= 0 ? '#28a745' : '#dc3545';
        }

        function generateTrialBalance() {
            const trialBalanceTableBody = document.getElementById('trialBalanceTable');
            let totalDebit = 0;
            let totalCredit = 0;

            trialBalanceTableBody.innerHTML = accounts.map(account => {
                const isDebitBalance = (account.type === 'asset' || account.type === 'expense') && account.balance > 0;
                const debitAmount = isDebitBalance ? account.balance : 0;
                const creditAmount = !isDebitBalance && account.balance > 0 ? account.balance : 0;

                totalDebit += debitAmount;
                totalCredit += creditAmount;

                return `
                    <tr>
                        <td>${account.id}</td>
                        <td>${account.name}</td>
                        <td>${debitAmount > 0 ? formatCurrency(debitAmount) : '-'}</td>
                        <td>${creditAmount > 0 ? formatCurrency(creditAmount) : '-'}</td>
                    </tr>
                `;
            }).join('');

            document.getElementById('totalDebitBalance').textContent = formatCurrency(totalDebit);
            document.getElementById('totalCreditBalance').textContent = formatCurrency(totalCredit);
        }

        function generateCashFlow() {
            // حساب التدفقات النقدية (مبسط)
            const cashAccount = accounts.find(acc => acc.name === 'النقدية');
            const bankAccount = accounts.find(acc => acc.name === 'البنك');
            
            const operatingFlow = calculateTotalRevenue() - calculateTotalExpenses();
            const investingFlow = 0; // يمكن تطويره لاحقاً
            const financingFlow = 0; // يمكن تطويره لاحقاً
            const netFlow = operatingFlow + investingFlow + financingFlow;

            document.getElementById('operatingCashFlow').textContent = formatCurrency(operatingFlow);
            document.getElementById('investingCashFlow').textContent = formatCurrency(investingFlow);
            document.getElementById('financingCashFlow').textContent = formatCurrency(financingFlow);
            document.getElementById('netCashFlow').textContent = formatCurrency(netFlow);

            // إنشاء الرسم البياني
            createCashFlowChart([operatingFlow, investingFlow, financingFlow]);
        }

        function createCashFlowChart(data) {
            const ctx = document.getElementById('cashFlowChart').getContext('2d');
            
            if (cashFlowChart) {
                cashFlowChart.destroy();
            }

            cashFlowChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['العمليات التشغيلية', 'الأنشطة الاستثمارية', 'الأنشطة التمويلية'],
                    datasets: [{
                        label: 'التدفق النقدي (ر.س)',
                        data: data,
                        backgroundColor: [
                            'rgba(102, 126, 234, 0.8)',
                            'rgba(118, 75, 162, 0.8)',
                            'rgba(40, 167, 69, 0.8)'
                        ],
                        borderColor: [
                            'rgba(102, 126, 234, 1)',
                            'rgba(118, 75, 162, 1)',
                            'rgba(40, 167, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function exportReport() {
            showMessage('ميزة التصدير قيد التطوير', 'info');
        }

        function printReport() {
            const reportContent = document.querySelector('.report-section.active').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>طباعة التقرير</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 2rem; }
                        th, td { padding: 0.5rem; border: 1px solid #ddd; text-align: right; }
                        th { background-color: #f8f9fa; }
                        .total-row { background-color: #e9ecef; font-weight: bold; }
                        h2, h3 { color: #333; }
                    </style>
                </head>
                <body>
                    ${reportContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
