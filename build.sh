#!/bin/bash

# تعيين الترميز
export LANG=ar_SA.UTF-8

echo "========================================"
echo "   بناء برنامج المحاسبة - إصدار سطح المكتب"
echo "========================================"
echo

# التحقق من وجود Node.js
echo "التحقق من وجود Node.js..."
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت!"
    echo "يرجى تحميل وتثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "Node.js موجود ✓"
echo "الإصدار: $(node --version)"
echo

# التحقق من وجود npm
echo "التحقق من وجود npm..."
if ! command -v npm &> /dev/null; then
    echo "خطأ: npm غير متاح!"
    exit 1
fi

echo "npm موجود ✓"
echo "الإصدار: $(npm --version)"
echo

# تثبيت التبعيات
echo "تثبيت التبعيات..."
npm install
if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت التبعيات!"
    exit 1
fi

echo "تم تثبيت التبعيات بنجاح ✓"
echo

# تحديد نظام التشغيل
OS="$(uname -s)"
case "${OS}" in
    Linux*)     PLATFORM=linux;;
    Darwin*)    PLATFORM=mac;;
    *)          PLATFORM=linux;;
esac

echo "نظام التشغيل المكتشف: ${OS}"
echo "بناء التطبيق لنظام: ${PLATFORM}"
echo

# بناء التطبيق
if [ "$PLATFORM" = "mac" ]; then
    npm run build-mac
else
    npm run build-linux
fi

if [ $? -ne 0 ]; then
    echo "خطأ في بناء التطبيق!"
    exit 1
fi

echo
echo "========================================"
echo "تم بناء التطبيق بنجاح! 🎉"
echo "========================================"
echo

echo "ستجد الملفات في مجلد: dist/"
echo

echo "الملفات المتاحة:"
ls -la dist/ 2>/dev/null | grep -E '\.(dmg|AppImage|deb|rpm)$' || echo "لا توجد ملفات تنفيذية"
echo

# السؤال عن فتح مجلد الإخراج
read -p "هل تريد فتح مجلد الإخراج؟ (y/n): " choice
case "$choice" in
    y|Y|yes|YES )
        if command -v xdg-open &> /dev/null; then
            xdg-open dist/
        elif command -v open &> /dev/null; then
            open dist/
        else
            echo "لا يمكن فتح مجلد الإخراج تلقائياً"
        fi
        ;;
    * )
        echo "تم الانتهاء."
        ;;
esac

echo
echo "شكراً لاستخدام برنامج المحاسبة!"
