// بيانات المخزون
let inventoryItems = JSON.parse(localStorage.getItem('inventoryItems')) || [];
let stockMovements = JSON.parse(localStorage.getItem('stockMovements')) || [];

// متغيرات التحكم
let currentItemToDelete = null;
let currentItemsPage = 1;
let currentCategoryFilter = 'all';
let currentStockFilter = 'all';
const itemsPerPage = 10;
let filteredItems = [];

// تهيئة صفحة المخزون
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('inventory.html')) {
        loadInventoryPage();
        setupInventoryEventListeners();
    }
});

function loadInventoryPage() {
    filteredItems = [...inventoryItems];
    displayInventoryItems();
    updateInventoryStats();
    updateMovementItemSelect();
}

function setupInventoryEventListeners() {
    // البحث في المواد
    document.getElementById('searchItems').addEventListener('input', function() {
        filterInventoryItems();
    });

    // فلاتر الفئات
    document.querySelectorAll('.category-filters .filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.category-filters .filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentCategoryFilter = this.dataset.category;
            filterInventoryItems();
        });
    });

    // فلاتر المخزون
    document.querySelectorAll('.stock-filters .filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.stock-filters .filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentStockFilter = this.dataset.stock;
            filterInventoryItems();
        });
    });

    // نماذج الإدخال
    document.getElementById('newItemForm').addEventListener('submit', handleNewItem);
    document.getElementById('stockMovementForm').addEventListener('submit', handleStockMovement);
    document.getElementById('editItemForm').addEventListener('submit', handleEditItem);

    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('movementDate').value = today;

    // إنشاء كود تلقائي للمادة
    document.getElementById('itemName').addEventListener('input', function() {
        if (!document.getElementById('itemCode').value) {
            generateItemCode();
        }
    });
}

function filterInventoryItems() {
    const searchTerm = document.getElementById('searchItems').value.toLowerCase();
    
    filteredItems = inventoryItems.filter(item => {
        const matchesSearch = item.name.toLowerCase().includes(searchTerm) ||
                            item.code.toLowerCase().includes(searchTerm);
        
        const matchesCategory = currentCategoryFilter === 'all' || 
                              item.category === currentCategoryFilter;
        
        let matchesStock = true;
        if (currentStockFilter === 'low') {
            matchesStock = item.quantity <= item.minQuantity && item.quantity > 0;
        } else if (currentStockFilter === 'out') {
            matchesStock = item.quantity <= 0;
        }
        
        return matchesSearch && matchesCategory && matchesStock;
    });

    currentItemsPage = 1;
    displayInventoryItems();
}

function displayInventoryItems() {
    const tableBody = document.querySelector('#inventoryTable tbody');
    const startIndex = (currentItemsPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = filteredItems.slice(startIndex, endIndex);

    if (pageItems.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="11" style="text-align: center;">لا توجد مواد</td></tr>';
        updateItemsPagination();
        return;
    }

    tableBody.innerHTML = pageItems.map(item => {
        const totalValue = item.quantity * item.purchasePrice;
        const status = getItemStatus(item);
        
        return `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${getCategoryText(item.category)}</td>
                <td>${getUnitText(item.unit)}</td>
                <td>${item.quantity}</td>
                <td>${item.minQuantity}</td>
                <td>${formatCurrency(item.purchasePrice)}</td>
                <td>${formatCurrency(item.salePrice)}</td>
                <td>${formatCurrency(totalValue)}</td>
                <td><span class="status-badge ${status.class}">${status.text}</span></td>
                <td>
                    <button class="btn-icon" onclick="viewItemMovements(${item.id})" title="حركات المخزون">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn-icon" onclick="editItem(${item.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteItem(${item.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    updateItemsPagination();
}

function updateItemsPagination() {
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
    document.getElementById('pageInfoItems').textContent = `صفحة ${currentItemsPage} من ${totalPages}`;
    document.getElementById('prevPageItems').disabled = currentItemsPage === 1;
    document.getElementById('nextPageItems').disabled = currentItemsPage === totalPages || totalPages === 0;
}

function changeItemsPage(direction) {
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
    const newPage = currentItemsPage + direction;
    
    if (newPage >= 1 && newPage <= totalPages) {
        currentItemsPage = newPage;
        displayInventoryItems();
    }
}

function updateInventoryStats() {
    const totalItemsCount = inventoryItems.length;
    const totalValue = inventoryItems.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0);
    const lowStockCount = inventoryItems.filter(item => item.quantity <= item.minQuantity && item.quantity > 0).length;
    const outOfStockCount = inventoryItems.filter(item => item.quantity <= 0).length;

    document.getElementById('totalItems').textContent = totalItemsCount;
    document.getElementById('totalInventoryValue').textContent = formatCurrency(totalValue);
    document.getElementById('lowStockItems').textContent = lowStockCount;
    document.getElementById('outOfStockItems').textContent = outOfStockCount;
}

function getCategoryText(category) {
    const categories = {
        'products': 'منتجات',
        'materials': 'مواد خام',
        'supplies': 'مستلزمات',
        'equipment': 'معدات'
    };
    return categories[category] || category;
}

function getUnitText(unit) {
    const units = {
        'piece': 'قطعة',
        'kg': 'كيلوجرام',
        'liter': 'لتر',
        'meter': 'متر',
        'box': 'صندوق',
        'pack': 'عبوة'
    };
    return units[unit] || unit;
}

function getItemStatus(item) {
    if (item.quantity <= 0) {
        return { class: 'out-of-stock', text: 'نفد المخزون' };
    } else if (item.quantity <= item.minQuantity) {
        return { class: 'low-stock', text: 'مخزون منخفض' };
    } else {
        return { class: 'in-stock', text: 'متوفر' };
    }
}

function generateItemCode() {
    const name = document.getElementById('itemName').value;
    const category = document.getElementById('itemCategory').value;
    
    if (name && category) {
        const nameCode = name.substring(0, 3).toUpperCase();
        const categoryCode = category.substring(0, 2).toUpperCase();
        const number = String(inventoryItems.length + 1).padStart(3, '0');
        const code = `${categoryCode}${nameCode}${number}`;
        document.getElementById('itemCode').value = code;
    }
}

function handleNewItem(event) {
    event.preventDefault();

    const item = {
        id: Date.now(),
        code: document.getElementById('itemCode').value,
        name: document.getElementById('itemName').value,
        category: document.getElementById('itemCategory').value,
        unit: document.getElementById('itemUnit').value,
        quantity: parseFloat(document.getElementById('initialQuantity').value),
        minQuantity: parseFloat(document.getElementById('minQuantity').value),
        purchasePrice: parseFloat(document.getElementById('purchasePrice').value),
        salePrice: parseFloat(document.getElementById('salePrice').value),
        description: document.getElementById('itemDescription').value,
        location: document.getElementById('itemLocation').value,
        createdDate: new Date().toISOString()
    };

    // التحقق من عدم تكرار الكود
    if (inventoryItems.some(existingItem => existingItem.code === item.code)) {
        showMessage('كود المادة موجود بالفعل', 'error');
        return;
    }

    // إنشاء حساب المخزون إذا لم يكن موجوداً
    createInventoryAccountIfNeeded();

    // إضافة المادة
    inventoryItems.push(item);

    // إضافة حركة مخزون افتتاحية إذا كانت الكمية أكبر من صفر
    if (item.quantity > 0) {
        const movement = {
            id: Date.now() + 1,
            itemId: item.id,
            date: new Date().toISOString().split('T')[0],
            type: 'in',
            quantity: item.quantity,
            reason: 'رصيد افتتاحي',
            reference: 'OPENING'
        };
        stockMovements.push(movement);
    }

    saveInventoryData();
    displayInventoryItems();
    updateInventoryStats();
    updateMovementItemSelect();
    closeModal('newItemModal');
    event.target.reset();

    showMessage('تم إضافة المادة بنجاح', 'success');
}

// إنشاء حساب المخزون إذا لم يكن موجوداً
function createInventoryAccountIfNeeded() {
    // الحصول على الحسابات من التخزين المحلي
    let accounts = JSON.parse(localStorage.getItem('accounts')) || [];

    // التحقق من وجود حساب المخزون
    const inventoryAccount = accounts.find(acc => acc.name === 'المخزون');

    if (!inventoryAccount) {
        // إنشاء حساب المخزون
        const newAccount = {
            id: Date.now(),
            name: 'المخزون',
            type: 'asset',
            balance: 0,
            description: 'حساب المخزون - تم إنشاؤه تلقائياً'
        };

        accounts.push(newAccount);
        localStorage.setItem('accounts', JSON.stringify(accounts));

        showMessage('تم إنشاء حساب المخزون تلقائياً', 'info');
    }
}

function handleStockMovement(event) {
    event.preventDefault();
    
    const movement = {
        id: Date.now(),
        itemId: parseInt(document.getElementById('movementItem').value),
        date: document.getElementById('movementDate').value,
        type: document.getElementById('movementType').value,
        quantity: parseFloat(document.getElementById('movementQuantity').value),
        reason: document.getElementById('movementReason').value,
        reference: document.getElementById('movementReference').value
    };

    // العثور على المادة
    const item = inventoryItems.find(item => item.id === movement.itemId);
    if (!item) {
        showMessage('المادة غير موجودة', 'error');
        return;
    }

    // تحديث الكمية حسب نوع الحركة
    let newQuantity = item.quantity;
    if (movement.type === 'in') {
        newQuantity += movement.quantity;
    } else if (movement.type === 'out') {
        newQuantity -= movement.quantity;
        if (newQuantity < 0) {
            showMessage('الكمية المطلوبة أكبر من المتاح في المخزون', 'error');
            return;
        }
    } else if (movement.type === 'adjustment') {
        newQuantity = movement.quantity;
    }

    // تحديث كمية المادة
    item.quantity = newQuantity;
    
    // إضافة الحركة
    stockMovements.push(movement);
    
    saveInventoryData();
    displayInventoryItems();
    updateInventoryStats();
    closeModal('stockMovementModal');
    event.target.reset();
    
    showMessage('تم تنفيذ حركة المخزون بنجاح', 'success');
}

function updateMovementItemSelect() {
    const select = document.getElementById('movementItem');
    const editSelect = document.getElementById('editMovementItem');
    
    const options = inventoryItems.map(item => 
        `<option value="${item.id}">${item.code} - ${item.name}</option>`
    ).join('');
    
    select.innerHTML = '<option value="">اختر المادة</option>' + options;
    if (editSelect) {
        editSelect.innerHTML = '<option value="">اختر المادة</option>' + options;
    }
}

function editItem(itemId) {
    const item = inventoryItems.find(item => item.id === itemId);
    if (!item) return;

    document.getElementById('editItemId').value = item.id;
    document.getElementById('editItemCode').value = item.code;
    document.getElementById('editItemName').value = item.name;
    document.getElementById('editItemCategory').value = item.category;
    document.getElementById('editItemUnit').value = item.unit;
    document.getElementById('editCurrentQuantity').value = item.quantity;
    document.getElementById('editMinQuantity').value = item.minQuantity;
    document.getElementById('editPurchasePrice').value = item.purchasePrice;
    document.getElementById('editSalePrice').value = item.salePrice;
    document.getElementById('editItemDescription').value = item.description || '';
    document.getElementById('editItemLocation').value = item.location || '';

    openModal('editItemModal');
}

function handleEditItem(event) {
    event.preventDefault();
    
    const itemId = parseInt(document.getElementById('editItemId').value);
    const item = inventoryItems.find(item => item.id === itemId);
    
    if (!item) return;

    // التحقق من عدم تكرار الكود مع مواد أخرى
    const newCode = document.getElementById('editItemCode').value;
    if (inventoryItems.some(existingItem => existingItem.code === newCode && existingItem.id !== itemId)) {
        showMessage('كود المادة موجود بالفعل', 'error');
        return;
    }

    item.code = newCode;
    item.name = document.getElementById('editItemName').value;
    item.category = document.getElementById('editItemCategory').value;
    item.unit = document.getElementById('editItemUnit').value;
    item.minQuantity = parseFloat(document.getElementById('editMinQuantity').value);
    item.purchasePrice = parseFloat(document.getElementById('editPurchasePrice').value);
    item.salePrice = parseFloat(document.getElementById('editSalePrice').value);
    item.description = document.getElementById('editItemDescription').value;
    item.location = document.getElementById('editItemLocation').value;

    saveInventoryData();
    displayInventoryItems();
    updateInventoryStats();
    updateMovementItemSelect();
    closeModal('editItemModal');
    showMessage('تم تحديث المادة بنجاح', 'success');
}

function deleteItem(itemId) {
    currentItemToDelete = itemId;
    openModal('deleteItemModal');
}

function confirmDeleteItem() {
    if (!currentItemToDelete) return;

    // حذف المادة
    inventoryItems = inventoryItems.filter(item => item.id !== currentItemToDelete);
    
    // حذف حركات المخزون المرتبطة
    stockMovements = stockMovements.filter(movement => movement.itemId !== currentItemToDelete);

    saveInventoryData();
    displayInventoryItems();
    updateInventoryStats();
    updateMovementItemSelect();
    closeModal('deleteItemModal');
    showMessage('تم حذف المادة بنجاح', 'success');
    
    currentItemToDelete = null;
}

function viewItemMovements(itemId) {
    const item = inventoryItems.find(item => item.id === itemId);
    const movements = stockMovements.filter(movement => movement.itemId === itemId);
    
    if (!item) return;
    
    let movementsHTML = `
        <h3>حركات مخزون: ${item.name}</h3>
        <table class="report-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>النوع</th>
                    <th>الكمية</th>
                    <th>السبب</th>
                    <th>المرجع</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    if (movements.length === 0) {
        movementsHTML += '<tr><td colspan="5" style="text-align: center;">لا توجد حركات</td></tr>';
    } else {
        movementsHTML += movements.map(movement => `
            <tr>
                <td>${formatDate(movement.date)}</td>
                <td>${getMovementTypeText(movement.type)}</td>
                <td>${movement.quantity}</td>
                <td>${movement.reason}</td>
                <td>${movement.reference || '-'}</td>
            </tr>
        `).join('');
    }
    
    movementsHTML += '</tbody></table>';
    
    // عرض النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>حركات المخزون</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div style="padding: 2rem;">
                ${movementsHTML}
                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function getMovementTypeText(type) {
    const types = {
        'in': 'إدخال',
        'out': 'إخراج',
        'adjustment': 'تسوية',
        'transfer': 'نقل'
    };
    return types[type] || type;
}

function saveInventoryData() {
    localStorage.setItem('inventoryItems', JSON.stringify(inventoryItems));
    localStorage.setItem('stockMovements', JSON.stringify(stockMovements));
}

// دوال مساعدة للتكامل مع النظام الرئيسي
function getInventoryValue() {
    return inventoryItems.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0);
}

function getLowStockItems() {
    return inventoryItems.filter(item => item.quantity <= item.minQuantity && item.quantity > 0);
}

function getOutOfStockItems() {
    return inventoryItems.filter(item => item.quantity <= 0);
}
