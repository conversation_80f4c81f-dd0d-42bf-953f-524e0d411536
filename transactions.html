<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القيود المحاسبية - برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link active">القيود</a></li>
                <li><a href="invoices.html" class="nav-link">الفواتير</a></li>
                <li><a href="reports.html" class="nav-link">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1>القيود المحاسبية</h1>
            <button class="btn btn-primary" onclick="openModal('newTransactionModal')">
                <i class="fas fa-plus"></i>
                إضافة قيد جديد
            </button>
        </div>

        <!-- فلاتر البحث والتاريخ -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="search-box">
                    <input type="text" id="searchTransactions" placeholder="البحث في القيود...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="date-filters">
                    <input type="date" id="fromDate" placeholder="من تاريخ">
                    <input type="date" id="toDate" placeholder="إلى تاريخ">
                    <button class="btn btn-secondary" onclick="filterByDate()">فلترة</button>
                    <button class="btn btn-secondary" onclick="clearDateFilter()">مسح</button>
                </div>
            </div>
        </div>

        <!-- إحصائيات القيود -->
        <div class="transactions-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي القيود</h3>
                    <p id="totalTransactions">0</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي المدين</h3>
                    <p id="totalDebit">0 ر.س</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-minus-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي الدائن</h3>
                    <p id="totalCredit">0 ر.س</p>
                </div>
            </div>
        </div>

        <!-- جدول القيود -->
        <div class="transactions-table-container">
            <table id="transactionsTable">
                <thead>
                    <tr>
                        <th>رقم القيد</th>
                        <th>التاريخ</th>
                        <th>الوصف</th>
                        <th>الحساب المدين</th>
                        <th>الحساب الدائن</th>
                        <th>المبلغ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        <div class="pagination">
            <button id="prevPage" class="btn btn-secondary" onclick="changePage(-1)">السابق</button>
            <span id="pageInfo">صفحة 1 من 1</span>
            <button id="nextPage" class="btn btn-secondary" onclick="changePage(1)">التالي</button>
        </div>
    </main>

    <!-- نافذة منبثقة لقيد جديد -->
    <div id="newTransactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>قيد محاسبي جديد</h3>
                <span class="close" onclick="closeModal('newTransactionModal')">&times;</span>
            </div>
            <form id="newTransactionForm">
                <div class="form-group">
                    <label for="transactionDate">التاريخ:</label>
                    <input type="date" id="transactionDate" required>
                </div>
                <div class="form-group">
                    <label for="transactionDescription">الوصف:</label>
                    <input type="text" id="transactionDescription" required>
                </div>
                <div class="form-group">
                    <label for="debitAccount">الحساب المدين:</label>
                    <select id="debitAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="creditAccount">الحساب الدائن:</label>
                    <select id="creditAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="transactionAmount">المبلغ:</label>
                    <input type="number" id="transactionAmount" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="transactionNotes">ملاحظات:</label>
                    <textarea id="transactionNotes" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newTransactionModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لتعديل القيد -->
    <div id="editTransactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل القيد</h3>
                <span class="close" onclick="closeModal('editTransactionModal')">&times;</span>
            </div>
            <form id="editTransactionForm">
                <input type="hidden" id="editTransactionId">
                <div class="form-group">
                    <label for="editTransactionDate">التاريخ:</label>
                    <input type="date" id="editTransactionDate" required>
                </div>
                <div class="form-group">
                    <label for="editTransactionDescription">الوصف:</label>
                    <input type="text" id="editTransactionDescription" required>
                </div>
                <div class="form-group">
                    <label for="editDebitAccount">الحساب المدين:</label>
                    <select id="editDebitAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editCreditAccount">الحساب الدائن:</label>
                    <select id="editCreditAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editTransactionAmount">المبلغ:</label>
                    <input type="number" id="editTransactionAmount" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="editTransactionNotes">ملاحظات:</label>
                    <textarea id="editTransactionNotes" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editTransactionModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteTransactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <span class="close" onclick="closeModal('deleteTransactionModal')">&times;</span>
            </div>
            <div style="padding: 2rem;">
                <p>هل أنت متأكد من حذف هذا القيد؟</p>
                <p><strong>تحذير:</strong> سيتم تعديل أرصدة الحسابات المرتبطة.</p>
                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-danger" onclick="confirmDeleteTransaction()">حذف</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteTransactionModal')">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        let currentTransactionToDelete = null;
        let currentPage = 1;
        const itemsPerPage = 10;
        let filteredTransactions = [];

        // تهيئة صفحة القيود
        document.addEventListener('DOMContentLoaded', function() {
            loadTransactionsPage();
            setupTransactionsEventListeners();
        });

        function loadTransactionsPage() {
            filteredTransactions = [...transactions];
            displayTransactions();
            updateTransactionsStats();
            updateAccountSelects();
        }

        function setupTransactionsEventListeners() {
            // البحث في القيود
            document.getElementById('searchTransactions').addEventListener('input', function() {
                filterTransactions();
            });

            // نماذج التعديل
            document.getElementById('editTransactionForm').addEventListener('submit', handleEditTransaction);
        }

        function filterTransactions() {
            const searchTerm = document.getElementById('searchTransactions').value.toLowerCase();
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;

            filteredTransactions = transactions.filter(transaction => {
                const matchesSearch = transaction.description.toLowerCase().includes(searchTerm);
                const matchesDateRange = (!fromDate || transaction.date >= fromDate) && 
                                       (!toDate || transaction.date <= toDate);
                return matchesSearch && matchesDateRange;
            });

            currentPage = 1;
            displayTransactions();
        }

        function filterByDate() {
            filterTransactions();
        }

        function clearDateFilter() {
            document.getElementById('fromDate').value = '';
            document.getElementById('toDate').value = '';
            filterTransactions();
        }

        function displayTransactions() {
            const tableBody = document.querySelector('#transactionsTable tbody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageTransactions = filteredTransactions.slice(startIndex, endIndex);

            if (pageTransactions.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد قيود</td></tr>';
                updatePagination();
                return;
            }

            tableBody.innerHTML = pageTransactions.map(transaction => {
                const debitAccount = accounts.find(acc => acc.id == transaction.debitAccountId);
                const creditAccount = accounts.find(acc => acc.id == transaction.creditAccountId);
                
                return `
                    <tr>
                        <td>${transaction.id}</td>
                        <td>${formatDate(transaction.date)}</td>
                        <td>${transaction.description}</td>
                        <td>${debitAccount ? debitAccount.name : 'غير محدد'}</td>
                        <td>${creditAccount ? creditAccount.name : 'غير محدد'}</td>
                        <td>${formatCurrency(transaction.amount)}</td>
                        <td>
                            <button class="btn-icon" onclick="editTransaction(${transaction.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon btn-danger" onclick="deleteTransaction(${transaction.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            updatePagination();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
            document.getElementById('pageInfo').textContent = `صفحة ${currentPage} من ${totalPages}`;
            document.getElementById('prevPage').disabled = currentPage === 1;
            document.getElementById('nextPage').disabled = currentPage === totalPages || totalPages === 0;
        }

        function changePage(direction) {
            const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
            const newPage = currentPage + direction;
            
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                displayTransactions();
            }
        }

        function updateTransactionsStats() {
            const totalTransactionsCount = transactions.length;
            const totalDebitAmount = transactions.reduce((sum, trans) => sum + trans.amount, 0);
            const totalCreditAmount = totalDebitAmount; // في المحاسبة المزدوجة المدين = الدائن

            document.getElementById('totalTransactions').textContent = totalTransactionsCount;
            document.getElementById('totalDebit').textContent = formatCurrency(totalDebitAmount);
            document.getElementById('totalCredit').textContent = formatCurrency(totalCreditAmount);
        }

        function editTransaction(transactionId) {
            const transaction = transactions.find(trans => trans.id === transactionId);
            if (!transaction) return;

            document.getElementById('editTransactionId').value = transaction.id;
            document.getElementById('editTransactionDate').value = transaction.date;
            document.getElementById('editTransactionDescription').value = transaction.description;
            document.getElementById('editDebitAccount').value = transaction.debitAccountId;
            document.getElementById('editCreditAccount').value = transaction.creditAccountId;
            document.getElementById('editTransactionAmount').value = transaction.amount;
            document.getElementById('editTransactionNotes').value = transaction.notes || '';

            // تحديث قوائم الحسابات في نموذج التعديل
            updateEditAccountSelects();
            openModal('editTransactionModal');
        }

        function updateEditAccountSelects() {
            const editDebitSelect = document.getElementById('editDebitAccount');
            const editCreditSelect = document.getElementById('editCreditAccount');
            
            const accountOptions = accounts.map(account => 
                `<option value="${account.id}">${account.name}</option>`
            ).join('');
            
            editDebitSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
            editCreditSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
        }

        function handleEditTransaction(event) {
            event.preventDefault();
            
            const transactionId = parseInt(document.getElementById('editTransactionId').value);
            const transaction = transactions.find(trans => trans.id === transactionId);
            
            if (!transaction) return;

            // عكس التأثير السابق على الحسابات
            updateAccountBalance(transaction.debitAccountId, transaction.amount, 'credit');
            updateAccountBalance(transaction.creditAccountId, transaction.amount, 'debit');

            // تحديث بيانات القيد
            transaction.date = document.getElementById('editTransactionDate').value;
            transaction.description = document.getElementById('editTransactionDescription').value;
            transaction.debitAccountId = parseInt(document.getElementById('editDebitAccount').value);
            transaction.creditAccountId = parseInt(document.getElementById('editCreditAccount').value);
            transaction.amount = parseFloat(document.getElementById('editTransactionAmount').value);
            transaction.notes = document.getElementById('editTransactionNotes').value;

            // تطبيق التأثير الجديد على الحسابات
            updateAccountBalance(transaction.debitAccountId, transaction.amount, 'debit');
            updateAccountBalance(transaction.creditAccountId, transaction.amount, 'credit');

            saveData();
            displayTransactions();
            updateTransactionsStats();
            closeModal('editTransactionModal');
            showMessage('تم تحديث القيد بنجاح', 'success');
        }

        function deleteTransaction(transactionId) {
            currentTransactionToDelete = transactionId;
            openModal('deleteTransactionModal');
        }

        function confirmDeleteTransaction() {
            if (!currentTransactionToDelete) return;

            const transaction = transactions.find(trans => trans.id === currentTransactionToDelete);
            if (transaction) {
                // عكس تأثير القيد على الحسابات
                updateAccountBalance(transaction.debitAccountId, transaction.amount, 'credit');
                updateAccountBalance(transaction.creditAccountId, transaction.amount, 'debit');
            }

            // حذف القيد
            transactions = transactions.filter(trans => trans.id !== currentTransactionToDelete);
            filteredTransactions = filteredTransactions.filter(trans => trans.id !== currentTransactionToDelete);

            saveData();
            displayTransactions();
            updateTransactionsStats();
            closeModal('deleteTransactionModal');
            showMessage('تم حذف القيد بنجاح', 'success');
            
            currentTransactionToDelete = null;
        }
    </script>
</body>
</html>
