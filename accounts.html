<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات - برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link active">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link">القيود</a></li>
                <li><a href="invoices.html" class="nav-link">الفواتير</a></li>
                <li><a href="reports.html" class="nav-link">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1>إدارة الحسابات</h1>
            <button class="btn btn-primary" onclick="openModal('newAccountModal')">
                <i class="fas fa-plus"></i>
                إضافة حساب جديد
            </button>
        </div>

        <!-- فلاتر البحث -->
        <div class="filters-section">
            <div class="search-box">
                <input type="text" id="searchAccounts" placeholder="البحث في الحسابات...">
                <i class="fas fa-search"></i>
            </div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-type="all">جميع الحسابات</button>
                <button class="filter-btn" data-type="asset">الأصول</button>
                <button class="filter-btn" data-type="liability">الخصوم</button>
                <button class="filter-btn" data-type="equity">حقوق الملكية</button>
                <button class="filter-btn" data-type="revenue">الإيرادات</button>
                <button class="filter-btn" data-type="expense">المصروفات</button>
            </div>
        </div>

        <!-- جدول الحسابات -->
        <div class="accounts-table-container">
            <table id="accountsTable">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>نوع الحساب</th>
                        <th>الرصيد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- إحصائيات الحسابات -->
        <div class="accounts-stats">
            <div class="stat-card">
                <h3>إجمالي الأصول</h3>
                <p id="totalAssets">0 ر.س</p>
            </div>
            <div class="stat-card">
                <h3>إجمالي الخصوم</h3>
                <p id="totalLiabilities">0 ر.س</p>
            </div>
            <div class="stat-card">
                <h3>حقوق الملكية</h3>
                <p id="totalEquity">0 ر.س</p>
            </div>
        </div>
    </main>

    <!-- نافذة منبثقة لحساب جديد -->
    <div id="newAccountModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة حساب جديد</h3>
                <span class="close" onclick="closeModal('newAccountModal')">&times;</span>
            </div>
            <form id="newAccountForm">
                <div class="form-group">
                    <label for="accountName">اسم الحساب:</label>
                    <input type="text" id="accountName" required>
                </div>
                <div class="form-group">
                    <label for="accountType">نوع الحساب:</label>
                    <select id="accountType" required>
                        <option value="">اختر النوع</option>
                        <option value="asset">أصول</option>
                        <option value="liability">خصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">إيرادات</option>
                        <option value="expense">مصروفات</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="initialBalance">الرصيد الافتتاحي:</label>
                    <input type="number" id="initialBalance" step="0.01" value="0">
                </div>
                <div class="form-group">
                    <label for="accountDescription">وصف الحساب:</label>
                    <textarea id="accountDescription" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newAccountModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لتعديل الحساب -->
    <div id="editAccountModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل الحساب</h3>
                <span class="close" onclick="closeModal('editAccountModal')">&times;</span>
            </div>
            <form id="editAccountForm">
                <input type="hidden" id="editAccountId">
                <div class="form-group">
                    <label for="editAccountName">اسم الحساب:</label>
                    <input type="text" id="editAccountName" required>
                </div>
                <div class="form-group">
                    <label for="editAccountType">نوع الحساب:</label>
                    <select id="editAccountType" required>
                        <option value="asset">أصول</option>
                        <option value="liability">خصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">إيرادات</option>
                        <option value="expense">مصروفات</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editAccountBalance">الرصيد الحالي:</label>
                    <input type="number" id="editAccountBalance" step="0.01" readonly>
                </div>
                <div class="form-group">
                    <label for="editAccountDescription">وصف الحساب:</label>
                    <textarea id="editAccountDescription" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editAccountModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <span class="close" onclick="closeModal('deleteConfirmModal')">&times;</span>
            </div>
            <div style="padding: 2rem;">
                <p>هل أنت متأكد من حذف هذا الحساب؟</p>
                <p><strong>تحذير:</strong> سيتم حذف جميع المعاملات المرتبطة بهذا الحساب.</p>
                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-danger" onclick="confirmDeleteAccount()">حذف</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteConfirmModal')">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        let currentAccountToDelete = null;
        let currentFilter = 'all';

        // تهيئة صفحة الحسابات
        document.addEventListener('DOMContentLoaded', function() {
            loadAccountsPage();
            setupAccountsEventListeners();
        });

        function loadAccountsPage() {
            displayAccounts();
            updateAccountsStats();
        }

        function setupAccountsEventListeners() {
            // البحث في الحسابات
            document.getElementById('searchAccounts').addEventListener('input', function() {
                displayAccounts(this.value);
            });

            // فلاتر نوع الحساب
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.type;
                    displayAccounts();
                });
            });

            // نموذج تعديل الحساب
            document.getElementById('editAccountForm').addEventListener('submit', handleEditAccount);
        }

        function displayAccounts(searchTerm = '') {
            const tableBody = document.querySelector('#accountsTable tbody');
            let filteredAccounts = accounts;

            // تطبيق فلتر النوع
            if (currentFilter !== 'all') {
                filteredAccounts = filteredAccounts.filter(account => account.type === currentFilter);
            }

            // تطبيق البحث
            if (searchTerm) {
                filteredAccounts = filteredAccounts.filter(account => 
                    account.name.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }

            if (filteredAccounts.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد حسابات</td></tr>';
                return;
            }

            tableBody.innerHTML = filteredAccounts.map(account => `
                <tr>
                    <td>${account.id}</td>
                    <td>${account.name}</td>
                    <td>${getAccountTypeText(account.type)}</td>
                    <td>${formatCurrency(account.balance)}</td>
                    <td>
                        <button class="btn-icon" onclick="editAccount(${account.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteAccount(${account.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getAccountTypeText(type) {
            const types = {
                'asset': 'أصول',
                'liability': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expense': 'مصروفات'
            };
            return types[type] || type;
        }

        function updateAccountsStats() {
            const totalAssets = accounts
                .filter(acc => acc.type === 'asset')
                .reduce((sum, acc) => sum + acc.balance, 0);
            
            const totalLiabilities = accounts
                .filter(acc => acc.type === 'liability')
                .reduce((sum, acc) => sum + acc.balance, 0);
            
            const totalEquity = accounts
                .filter(acc => acc.type === 'equity')
                .reduce((sum, acc) => sum + acc.balance, 0);

            document.getElementById('totalAssets').textContent = formatCurrency(totalAssets);
            document.getElementById('totalLiabilities').textContent = formatCurrency(totalLiabilities);
            document.getElementById('totalEquity').textContent = formatCurrency(totalEquity);
        }

        function editAccount(accountId) {
            const account = accounts.find(acc => acc.id === accountId);
            if (!account) return;

            document.getElementById('editAccountId').value = account.id;
            document.getElementById('editAccountName').value = account.name;
            document.getElementById('editAccountType').value = account.type;
            document.getElementById('editAccountBalance').value = account.balance;
            document.getElementById('editAccountDescription').value = account.description || '';

            openModal('editAccountModal');
        }

        function handleEditAccount(event) {
            event.preventDefault();
            
            const accountId = parseInt(document.getElementById('editAccountId').value);
            const account = accounts.find(acc => acc.id === accountId);
            
            if (!account) return;

            account.name = document.getElementById('editAccountName').value;
            account.type = document.getElementById('editAccountType').value;
            account.description = document.getElementById('editAccountDescription').value;

            saveData();
            displayAccounts();
            updateAccountsStats();
            closeModal('editAccountModal');
            showMessage('تم تحديث الحساب بنجاح', 'success');
        }

        function deleteAccount(accountId) {
            currentAccountToDelete = accountId;
            openModal('deleteConfirmModal');
        }

        function confirmDeleteAccount() {
            if (!currentAccountToDelete) return;

            // حذف الحساب
            accounts = accounts.filter(acc => acc.id !== currentAccountToDelete);
            
            // حذف المعاملات المرتبطة
            transactions = transactions.filter(trans => 
                trans.debitAccountId !== currentAccountToDelete && 
                trans.creditAccountId !== currentAccountToDelete
            );

            saveData();
            displayAccounts();
            updateAccountsStats();
            closeModal('deleteConfirmModal');
            showMessage('تم حذف الحساب بنجاح', 'success');
            
            currentAccountToDelete = null;
        }
    </script>
</body>
</html>
