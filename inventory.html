<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد والمخزون - برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
                <div class="currency-indicator">
                    <i class="fas fa-coins"></i>
                    <span id="navCurrency">د.ع</span>
                </div>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link">القيود</a></li>
                <li><a href="invoices.html" class="nav-link">الفواتير</a></li>
                <li><a href="inventory.html" class="nav-link active">المواد</a></li>
                <li><a href="reports.html" class="nav-link">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1>إدارة المواد والمخزون</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openModal('newItemModal')">
                    <i class="fas fa-plus"></i>
                    إضافة مادة جديدة
                </button>
                <button class="btn btn-secondary" onclick="openModal('stockMovementModal')">
                    <i class="fas fa-exchange-alt"></i>
                    حركة مخزون
                </button>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="search-box">
                    <input type="text" id="searchItems" placeholder="البحث في المواد...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="category-filters">
                    <button class="filter-btn active" data-category="all">جميع المواد</button>
                    <button class="filter-btn" data-category="products">منتجات</button>
                    <button class="filter-btn" data-category="materials">مواد خام</button>
                    <button class="filter-btn" data-category="supplies">مستلزمات</button>
                    <button class="filter-btn" data-category="equipment">معدات</button>
                </div>
                <div class="stock-filters">
                    <button class="filter-btn" data-stock="low">مخزون منخفض</button>
                    <button class="filter-btn" data-stock="out">نفد المخزون</button>
                </div>
            </div>
        </div>

        <!-- إحصائيات المخزون -->
        <div class="inventory-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي المواد</h3>
                    <p id="totalItems">0</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-info">
                    <h3>قيمة المخزون</h3>
                    <p id="totalInventoryValue">0 ر.س</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-info">
                    <h3>مخزون منخفض</h3>
                    <p id="lowStockItems">0</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>نفد المخزون</h3>
                    <p id="outOfStockItems">0</p>
                </div>
            </div>
        </div>

        <!-- جدول المواد -->
        <div class="inventory-table-container">
            <table id="inventoryTable">
                <thead>
                    <tr>
                        <th>كود المادة</th>
                        <th>اسم المادة</th>
                        <th>الفئة</th>
                        <th>الوحدة</th>
                        <th>الكمية المتاحة</th>
                        <th>الحد الأدنى</th>
                        <th>سعر الشراء</th>
                        <th>سعر البيع</th>
                        <th>القيمة الإجمالية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        <div class="pagination">
            <button id="prevPageItems" class="btn btn-secondary" onclick="changeItemsPage(-1)">السابق</button>
            <span id="pageInfoItems">صفحة 1 من 1</span>
            <button id="nextPageItems" class="btn btn-secondary" onclick="changeItemsPage(1)">التالي</button>
        </div>
    </main>

    <!-- نافذة منبثقة لمادة جديدة -->
    <div id="newItemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة مادة جديدة</h3>
                <span class="close" onclick="closeModal('newItemModal')">&times;</span>
            </div>
            <form id="newItemForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemCode">كود المادة:</label>
                        <input type="text" id="itemCode" required>
                    </div>
                    <div class="form-group">
                        <label for="itemName">اسم المادة:</label>
                        <input type="text" id="itemName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemCategory">الفئة:</label>
                        <select id="itemCategory" required>
                            <option value="">اختر الفئة</option>
                            <option value="products">منتجات</option>
                            <option value="materials">مواد خام</option>
                            <option value="supplies">مستلزمات</option>
                            <option value="equipment">معدات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="itemUnit">الوحدة:</label>
                        <select id="itemUnit" required>
                            <option value="">اختر الوحدة</option>
                            <option value="piece">قطعة</option>
                            <option value="kg">كيلوجرام</option>
                            <option value="liter">لتر</option>
                            <option value="meter">متر</option>
                            <option value="box">صندوق</option>
                            <option value="pack">عبوة</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="initialQuantity">الكمية الافتتاحية:</label>
                        <input type="number" id="initialQuantity" step="0.01" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="minQuantity">الحد الأدنى:</label>
                        <input type="number" id="minQuantity" step="0.01" value="0" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchasePrice">سعر الشراء:</label>
                        <input type="number" id="purchasePrice" step="0.01" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="salePrice">سعر البيع:</label>
                        <input type="number" id="salePrice" step="0.01" value="0" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="itemDescription">الوصف:</label>
                    <textarea id="itemDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="itemLocation">موقع التخزين:</label>
                    <input type="text" id="itemLocation">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ المادة</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newItemModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لحركة المخزون -->
    <div id="stockMovementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>حركة مخزون</h3>
                <span class="close" onclick="closeModal('stockMovementModal')">&times;</span>
            </div>
            <form id="stockMovementForm">
                <div class="form-group">
                    <label for="movementDate">التاريخ:</label>
                    <input type="date" id="movementDate" required>
                </div>
                <div class="form-group">
                    <label for="movementItem">المادة:</label>
                    <select id="movementItem" required>
                        <option value="">اختر المادة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="movementType">نوع الحركة:</label>
                    <select id="movementType" required>
                        <option value="">اختر النوع</option>
                        <option value="in">إدخال</option>
                        <option value="out">إخراج</option>
                        <option value="adjustment">تسوية</option>
                        <option value="transfer">نقل</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="movementQuantity">الكمية:</label>
                    <input type="number" id="movementQuantity" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="movementReason">السبب:</label>
                    <textarea id="movementReason" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="movementReference">المرجع:</label>
                    <input type="text" id="movementReference" placeholder="رقم الفاتورة أو المرجع">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">تنفيذ الحركة</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('stockMovementModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لتعديل المادة -->
    <div id="editItemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل المادة</h3>
                <span class="close" onclick="closeModal('editItemModal')">&times;</span>
            </div>
            <form id="editItemForm">
                <input type="hidden" id="editItemId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editItemCode">كود المادة:</label>
                        <input type="text" id="editItemCode" required>
                    </div>
                    <div class="form-group">
                        <label for="editItemName">اسم المادة:</label>
                        <input type="text" id="editItemName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editItemCategory">الفئة:</label>
                        <select id="editItemCategory" required>
                            <option value="products">منتجات</option>
                            <option value="materials">مواد خام</option>
                            <option value="supplies">مستلزمات</option>
                            <option value="equipment">معدات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editItemUnit">الوحدة:</label>
                        <select id="editItemUnit" required>
                            <option value="piece">قطعة</option>
                            <option value="kg">كيلوجرام</option>
                            <option value="liter">لتر</option>
                            <option value="meter">متر</option>
                            <option value="box">صندوق</option>
                            <option value="pack">عبوة</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editMinQuantity">الحد الأدنى:</label>
                        <input type="number" id="editMinQuantity" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="editCurrentQuantity">الكمية الحالية:</label>
                        <input type="number" id="editCurrentQuantity" step="0.01" readonly>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editPurchasePrice">سعر الشراء:</label>
                        <input type="number" id="editPurchasePrice" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="editSalePrice">سعر البيع:</label>
                        <input type="number" id="editSalePrice" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editItemDescription">الوصف:</label>
                    <textarea id="editItemDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="editItemLocation">موقع التخزين:</label>
                    <input type="text" id="editItemLocation">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editItemModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteItemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <span class="close" onclick="closeModal('deleteItemModal')">&times;</span>
            </div>
            <div style="padding: 2rem;">
                <p>هل أنت متأكد من حذف هذه المادة؟</p>
                <p><strong>تحذير:</strong> سيتم حذف جميع حركات المخزون المرتبطة بهذه المادة.</p>
                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-danger" onclick="confirmDeleteItem()">حذف</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteItemModal')">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="inventory.js"></script>
</body>
</html>
