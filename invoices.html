<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفواتير - برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link">القيود</a></li>
                <li><a href="invoices.html" class="nav-link active">الفواتير</a></li>
                <li><a href="reports.html" class="nav-link">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1>إدارة الفواتير</h1>
            <button class="btn btn-primary" onclick="openModal('newInvoiceModal')">
                <i class="fas fa-plus"></i>
                فاتورة جديدة
            </button>
        </div>

        <!-- فلاتر البحث -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="search-box">
                    <input type="text" id="searchInvoices" placeholder="البحث في الفواتير...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="status-filters">
                    <button class="filter-btn active" data-status="all">جميع الفواتير</button>
                    <button class="filter-btn" data-status="paid">مدفوعة</button>
                    <button class="filter-btn" data-status="pending">معلقة</button>
                    <button class="filter-btn" data-status="overdue">متأخرة</button>
                </div>
            </div>
        </div>

        <!-- إحصائيات الفواتير -->
        <div class="invoices-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي الفواتير</h3>
                    <p id="totalInvoicesCount">0</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-info">
                    <h3>إجمالي المبيعات</h3>
                    <p id="totalSalesAmount">0 ر.س</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>الفواتير المدفوعة</h3>
                    <p id="paidInvoicesCount">0</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <h3>الفواتير المعلقة</h3>
                    <p id="pendingInvoicesCount">0</p>
                </div>
            </div>
        </div>

        <!-- جدول الفواتير -->
        <div class="invoices-table-container">
            <table id="invoicesTable">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة منبثقة لفاتورة جديدة -->
    <div id="newInvoiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>فاتورة جديدة</h3>
                <span class="close" onclick="closeModal('newInvoiceModal')">&times;</span>
            </div>
            <form id="newInvoiceForm">
                <div class="form-group">
                    <label for="invoiceDate">تاريخ الفاتورة:</label>
                    <input type="date" id="invoiceDate" required>
                </div>
                <div class="form-group">
                    <label for="customerName">اسم العميل:</label>
                    <input type="text" id="customerName" required>
                </div>
                <div class="form-group">
                    <label for="customerPhone">رقم الهاتف:</label>
                    <input type="tel" id="customerPhone">
                </div>
                <div class="form-group">
                    <label for="customerEmail">البريد الإلكتروني:</label>
                    <input type="email" id="customerEmail">
                </div>
                <div class="form-group">
                    <label for="invoiceDescription">وصف الخدمة/المنتج:</label>
                    <textarea id="invoiceDescription" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="invoiceAmount">المبلغ:</label>
                    <input type="number" id="invoiceAmount" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="dueDate">تاريخ الاستحقاق:</label>
                    <input type="date" id="dueDate">
                </div>
                <div class="form-group">
                    <label for="invoiceNotes">ملاحظات:</label>
                    <textarea id="invoiceNotes" rows="2"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ الفاتورة</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newInvoiceModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لعرض الفاتورة -->
    <div id="viewInvoiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>عرض الفاتورة</h3>
                <span class="close" onclick="closeModal('viewInvoiceModal')">&times;</span>
            </div>
            <div id="invoicePreview" class="invoice-preview">
                <!-- سيتم ملء محتوى الفاتورة هنا -->
            </div>
            <div class="form-actions" style="padding: 1rem 2rem;">
                <button class="btn btn-primary" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
                <button class="btn btn-success" onclick="markAsPaid()">
                    <i class="fas fa-check"></i>
                    تحديد كمدفوعة
                </button>
                <button class="btn btn-secondary" onclick="closeModal('viewInvoiceModal')">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteInvoiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <span class="close" onclick="closeModal('deleteInvoiceModal')">&times;</span>
            </div>
            <div style="padding: 2rem;">
                <p>هل أنت متأكد من حذف هذه الفاتورة؟</p>
                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-danger" onclick="confirmDeleteInvoice()">حذف</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteInvoiceModal')">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        let currentInvoiceToDelete = null;
        let currentInvoiceToView = null;
        let currentStatusFilter = 'all';

        // تهيئة صفحة الفواتير
        document.addEventListener('DOMContentLoaded', function() {
            loadInvoicesPage();
            setupInvoicesEventListeners();
        });

        function loadInvoicesPage() {
            displayInvoices();
            updateInvoicesStats();
        }

        function setupInvoicesEventListeners() {
            // البحث في الفواتير
            document.getElementById('searchInvoices').addEventListener('input', function() {
                displayInvoices();
            });

            // فلاتر الحالة
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentStatusFilter = this.dataset.status;
                    displayInvoices();
                });
            });

            // تعيين تاريخ الاستحقاق تلقائياً (30 يوم من تاريخ الفاتورة)
            document.getElementById('invoiceDate').addEventListener('change', function() {
                const invoiceDate = new Date(this.value);
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(dueDate.getDate() + 30);
                document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
            });
        }

        function displayInvoices() {
            const tableBody = document.querySelector('#invoicesTable tbody');
            const searchTerm = document.getElementById('searchInvoices').value.toLowerCase();
            
            let filteredInvoices = invoices.filter(invoice => {
                const matchesSearch = invoice.customerName.toLowerCase().includes(searchTerm) ||
                                    invoice.description.toLowerCase().includes(searchTerm);
                const matchesStatus = currentStatusFilter === 'all' || 
                                    getInvoiceStatus(invoice) === currentStatusFilter;
                return matchesSearch && matchesStatus;
            });

            if (filteredInvoices.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد فواتير</td></tr>';
                return;
            }

            tableBody.innerHTML = filteredInvoices.map(invoice => `
                <tr>
                    <td>${invoice.id}</td>
                    <td>${formatDate(invoice.date)}</td>
                    <td>${invoice.customerName}</td>
                    <td>${invoice.description}</td>
                    <td>${formatCurrency(invoice.amount)}</td>
                    <td><span class="status-badge ${getInvoiceStatus(invoice)}">${getStatusText(getInvoiceStatus(invoice))}</span></td>
                    <td>
                        <button class="btn-icon" onclick="viewInvoice(${invoice.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="editInvoice(${invoice.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteInvoice(${invoice.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getInvoiceStatus(invoice) {
            if (invoice.paid) return 'paid';
            
            const today = new Date();
            const dueDate = new Date(invoice.dueDate || invoice.date);
            dueDate.setDate(dueDate.getDate() + 30); // إذا لم يكن هناك تاريخ استحقاق، استخدم 30 يوم
            
            return today > dueDate ? 'overdue' : 'pending';
        }

        function getStatusText(status) {
            const statusTexts = {
                'paid': 'مدفوعة',
                'pending': 'معلقة',
                'overdue': 'متأخرة'
            };
            return statusTexts[status] || status;
        }

        function updateInvoicesStats() {
            const totalCount = invoices.length;
            const totalAmount = invoices.reduce((sum, inv) => sum + inv.amount, 0);
            const paidCount = invoices.filter(inv => inv.paid).length;
            const pendingCount = invoices.filter(inv => !inv.paid).length;

            document.getElementById('totalInvoicesCount').textContent = totalCount;
            document.getElementById('totalSalesAmount').textContent = formatCurrency(totalAmount);
            document.getElementById('paidInvoicesCount').textContent = paidCount;
            document.getElementById('pendingInvoicesCount').textContent = pendingCount;
        }

        function viewInvoice(invoiceId) {
            const invoice = invoices.find(inv => inv.id === invoiceId);
            if (!invoice) return;

            currentInvoiceToView = invoiceId;
            
            const invoiceHTML = `
                <div class="invoice-header">
                    <h2>فاتورة رقم: ${invoice.id}</h2>
                    <p>التاريخ: ${formatDate(invoice.date)}</p>
                </div>
                <div class="invoice-customer">
                    <h3>بيانات العميل:</h3>
                    <p><strong>الاسم:</strong> ${invoice.customerName}</p>
                    ${invoice.customerPhone ? `<p><strong>الهاتف:</strong> ${invoice.customerPhone}</p>` : ''}
                    ${invoice.customerEmail ? `<p><strong>البريد:</strong> ${invoice.customerEmail}</p>` : ''}
                </div>
                <div class="invoice-details">
                    <h3>تفاصيل الفاتورة:</h3>
                    <p><strong>الوصف:</strong> ${invoice.description}</p>
                    <p><strong>المبلغ:</strong> ${formatCurrency(invoice.amount)}</p>
                    <p><strong>الحالة:</strong> ${getStatusText(getInvoiceStatus(invoice))}</p>
                    ${invoice.dueDate ? `<p><strong>تاريخ الاستحقاق:</strong> ${formatDate(invoice.dueDate)}</p>` : ''}
                    ${invoice.notes ? `<p><strong>ملاحظات:</strong> ${invoice.notes}</p>` : ''}
                </div>
            `;

            document.getElementById('invoicePreview').innerHTML = invoiceHTML;
            openModal('viewInvoiceModal');
        }

        function editInvoice(invoiceId) {
            // يمكن إضافة نافذة تعديل الفاتورة هنا
            showMessage('ميزة تعديل الفاتورة قيد التطوير', 'info');
        }

        function deleteInvoice(invoiceId) {
            currentInvoiceToDelete = invoiceId;
            openModal('deleteInvoiceModal');
        }

        function confirmDeleteInvoice() {
            if (!currentInvoiceToDelete) return;

            // حذف الفاتورة
            invoices = invoices.filter(inv => inv.id !== currentInvoiceToDelete);

            saveData();
            displayInvoices();
            updateInvoicesStats();
            closeModal('deleteInvoiceModal');
            showMessage('تم حذف الفاتورة بنجاح', 'success');
            
            currentInvoiceToDelete = null;
        }

        function markAsPaid() {
            if (!currentInvoiceToView) return;

            const invoice = invoices.find(inv => inv.id === currentInvoiceToView);
            if (invoice) {
                invoice.paid = true;
                invoice.paidDate = new Date().toISOString().split('T')[0];
                
                saveData();
                displayInvoices();
                updateInvoicesStats();
                closeModal('viewInvoiceModal');
                showMessage('تم تحديد الفاتورة كمدفوعة', 'success');
            }
        }

        function printInvoice() {
            const invoiceContent = document.getElementById('invoicePreview').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>طباعة الفاتورة</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .invoice-header { text-align: center; margin-bottom: 2rem; }
                        .invoice-customer, .invoice-details { margin-bottom: 1.5rem; }
                        h2, h3 { color: #333; }
                        p { margin: 0.5rem 0; }
                    </style>
                </head>
                <body>
                    ${invoiceContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
