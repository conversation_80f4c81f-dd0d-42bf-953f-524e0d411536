<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج المحاسبة</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <meta name="description" content="برنامج محاسبة شامل لإدارة الحسابات والقيود والفواتير والتقارير المالية">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-calculator"></i>
                <span>برنامج المحاسبة</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link active">الرئيسية</a></li>
                <li><a href="accounts.html" class="nav-link">الحسابات</a></li>
                <li><a href="transactions.html" class="nav-link">القيود</a></li>
                <li><a href="invoices.html" class="nav-link">الفواتير</a></li>
                <li><a href="inventory.html" class="nav-link">المواد</a></li>
                <li><a href="reports.html" class="nav-link">التقارير</a></li>
                <li><a href="help.html" class="nav-link">المساعدة</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- لوحة التحكم -->
        <div class="dashboard">
            <h1>لوحة التحكم</h1>
            
            <!-- البطاقات الإحصائية -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3>إجمالي الإيرادات</h3>
                        <p class="stat-value" id="totalRevenue">0 ر.س</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-info">
                        <h3>إجمالي المصروفات</h3>
                        <p class="stat-value" id="totalExpenses">0 ر.س</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>صافي الربح</h3>
                        <p class="stat-value" id="netProfit">0 ر.س</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-info">
                        <h3>عدد الفواتير</h3>
                        <p class="stat-value" id="totalInvoices">0</p>
                    </div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h2>الإجراءات السريعة</h2>
                <div class="actions-grid">
                    <button class="action-btn" onclick="openModal('newTransactionModal')">
                        <i class="fas fa-plus"></i>
                        <span>قيد جديد</span>
                    </button>
                    <button class="action-btn" onclick="openModal('newInvoiceModal')">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>فاتورة جديدة</span>
                    </button>
                    <button class="action-btn" onclick="openModal('newAccountModal')">
                        <i class="fas fa-user-plus"></i>
                        <span>حساب جديد</span>
                    </button>
                    <button class="action-btn" onclick="window.location.href='inventory.html'">
                        <i class="fas fa-boxes"></i>
                        <span>إدارة المواد</span>
                    </button>
                    <button class="action-btn" onclick="generateReport()">
                        <i class="fas fa-chart-bar"></i>
                        <span>تقرير سريع</span>
                    </button>
                    <button class="action-btn" onclick="openModal('settingsModal')">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </button>
                    <button class="action-btn" onclick="validateData()">
                        <i class="fas fa-check-circle"></i>
                        <span>فحص البيانات</span>
                    </button>
                </div>
            </div>

            <!-- آخر المعاملات -->
            <div class="recent-transactions">
                <h2>آخر المعاملات</h2>
                <div class="table-container">
                    <table id="recentTransactionsTable">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>المدين</th>
                                <th>الدائن</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة منبثقة لقيد جديد -->
    <div id="newTransactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>قيد محاسبي جديد</h3>
                <span class="close" onclick="closeModal('newTransactionModal')">&times;</span>
            </div>
            <form id="newTransactionForm">
                <div class="form-group">
                    <label for="transactionDate">التاريخ:</label>
                    <input type="date" id="transactionDate" required>
                </div>
                <div class="form-group">
                    <label for="transactionDescription">الوصف:</label>
                    <input type="text" id="transactionDescription" required>
                </div>
                <div class="form-group">
                    <label for="debitAccount">الحساب المدين:</label>
                    <select id="debitAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="creditAccount">الحساب الدائن:</label>
                    <select id="creditAccount" required>
                        <option value="">اختر الحساب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="transactionAmount">المبلغ:</label>
                    <input type="number" id="transactionAmount" step="0.01" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newTransactionModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لفاتورة جديدة -->
    <div id="newInvoiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>فاتورة جديدة</h3>
                <span class="close" onclick="closeModal('newInvoiceModal')">&times;</span>
            </div>
            <form id="newInvoiceForm">
                <div class="form-group">
                    <label for="invoiceDate">التاريخ:</label>
                    <input type="date" id="invoiceDate" required>
                </div>
                <div class="form-group">
                    <label for="customerName">اسم العميل:</label>
                    <input type="text" id="customerName" required>
                </div>
                <div class="form-group">
                    <label for="invoiceDescription">الوصف:</label>
                    <input type="text" id="invoiceDescription" required>
                </div>
                <div class="form-group">
                    <label for="invoiceAmount">المبلغ:</label>
                    <input type="number" id="invoiceAmount" step="0.01" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newInvoiceModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة لحساب جديد -->
    <div id="newAccountModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>حساب جديد</h3>
                <span class="close" onclick="closeModal('newAccountModal')">&times;</span>
            </div>
            <form id="newAccountForm">
                <div class="form-group">
                    <label for="accountName">اسم الحساب:</label>
                    <input type="text" id="accountName" required>
                </div>
                <div class="form-group">
                    <label for="accountType">نوع الحساب:</label>
                    <select id="accountType" required>
                        <option value="">اختر النوع</option>
                        <option value="asset">أصول</option>
                        <option value="liability">خصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">إيرادات</option>
                        <option value="expense">مصروفات</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="initialBalance">الرصيد الافتتاحي:</label>
                    <input type="number" id="initialBalance" step="0.01" value="0">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newAccountModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة منبثقة للإعدادات -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إعدادات البرنامج</h3>
                <span class="close" onclick="closeModal('settingsModal')">&times;</span>
            </div>
            <div style="padding: 2rem;">
                <div class="settings-section">
                    <h4>إدارة البيانات</h4>
                    <div class="settings-actions">
                        <button class="btn btn-primary" onclick="exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                        <label for="importFile" class="btn btn-secondary">
                            <i class="fas fa-upload"></i>
                            استيراد البيانات
                        </label>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                        <button class="btn btn-danger" onclick="clearAllData()">
                            <i class="fas fa-trash"></i>
                            مسح جميع البيانات
                        </button>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>معلومات البرنامج</h4>
                    <p><strong>الإصدار:</strong> 1.0.0</p>
                    <p><strong>تاريخ الإنشاء:</strong> 2024</p>
                    <p><strong>المطور:</strong> برنامج المحاسبة</p>
                </div>

                <div class="form-actions" style="margin-top: 2rem;">
                    <button class="btn btn-secondary" onclick="closeModal('settingsModal')">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
