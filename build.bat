@echo off
chcp 65001 >nul
echo ========================================
echo    بناء برنامج المحاسبة - إصدار سطح المكتب
echo ========================================
echo.

echo التحقق من وجود Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js موجود ✓
echo.

echo التحقق من وجود npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: npm غير متاح!
    pause
    exit /b 1
)

echo npm موجود ✓
echo.

echo تثبيت التبعيات...
npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات!
    pause
    exit /b 1
)

echo تم تثبيت التبعيات بنجاح ✓
echo.

echo بناء التطبيق لنظام Windows...
npm run build-win
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق!
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء التطبيق بنجاح! 🎉
echo ========================================
echo.
echo ستجد الملفات في مجلد: dist\
echo.
echo الملفات المتاحة:
dir /b dist\*.exe 2>nul
echo.

echo هل تريد فتح مجلد الإخراج؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    start explorer dist
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
