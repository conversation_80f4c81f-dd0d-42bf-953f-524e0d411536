/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-logo i {
    margin-left: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* المحتوى الرئيسي */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.dashboard h1 {
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
}

/* البطاقات الإحصائية */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: 1rem;
}

.stat-info h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

/* الإجراءات السريعة */
.quick-actions {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.quick-actions h2 {
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.action-btn:hover {
    transform: translateY(-3px);
}

.action-btn i {
    font-size: 2rem;
}

/* آخر المعاملات */
.recent-transactions {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.recent-transactions h2 {
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

tr:hover {
    background-color: #f5f5f5;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    opacity: 0.7;
}

/* النماذج */
form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: bold;
}

input, select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

input:focus, select:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    opacity: 0.9;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(102, 126, 234, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .main-content {
        padding: 0 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* تحسينات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border: 1px solid #c3e6cb;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border: 1px solid #f5c6cb;
}

/* صفحة الحسابات */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.page-header h1 {
    margin: 0;
    color: #333;
}

.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    color: white;
}

.accounts-table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.accounts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.5rem;
    margin: 0 0.25rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    color: #667eea;
}

.btn-icon:hover {
    background-color: #f8f9fa;
}

.btn-icon.btn-danger {
    color: #dc3545;
}

.btn-icon.btn-danger:hover {
    background-color: #f8d7da;
}

textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* صفحة القيود والفواتير */
.filter-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.date-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.date-filters input {
    width: auto;
    min-width: 150px;
}

.transactions-stats,
.invoices-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.transactions-table-container,
.invoices-table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.status-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.paid {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.overdue {
    background-color: #f8d7da;
    color: #721c24;
}

/* عرض الفاتورة */
.invoice-preview {
    padding: 2rem;
    background: #f8f9fa;
    margin: 1rem;
    border-radius: 5px;
}

.invoice-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #667eea;
}

.invoice-customer,
.invoice-details {
    margin-bottom: 1.5rem;
}

.invoice-customer h3,
.invoice-details h3 {
    color: #667eea;
    margin-bottom: 1rem;
}

.invoice-customer p,
.invoice-details p {
    margin: 0.5rem 0;
    line-height: 1.6;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

/* تحسينات للتصميم المتجاوب */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .date-filters {
        justify-content: space-between;
    }

    .date-filters input {
        min-width: 120px;
    }

    .status-filters {
        justify-content: center;
    }

    .pagination {
        flex-direction: column;
        gap: 0.5rem;
    }

    .invoice-preview {
        padding: 1rem;
        margin: 0.5rem;
    }
}

/* صفحة التقارير */
.report-actions {
    display: flex;
    gap: 1rem;
}

.report-selector {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.report-tabs {
    display: flex;
    background: #f8f9fa;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: white;
    border-bottom-color: #667eea;
    color: #667eea;
    font-weight: bold;
}

.date-range-selector {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.date-inputs {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.date-inputs label {
    font-weight: bold;
    color: #333;
}

.date-inputs input {
    width: auto;
    min-width: 150px;
}

.report-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
}

.report-section {
    display: none;
    padding: 2rem;
}

.report-section.active {
    display: block;
}

.report-section h2 {
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    border-bottom: 2px solid #667eea;
    padding-bottom: 1rem;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.report-table th,
.report-table td {
    padding: 0.75rem;
    text-align: right;
    border: 1px solid #ddd;
}

.report-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.report-table .section-header td {
    background-color: #e9ecef;
    font-weight: bold;
    color: #495057;
}

.report-table .subtotal-row td,
.report-table .total-row td {
    background-color: #f8f9fa;
    font-weight: bold;
}

.report-table .total-row td {
    background-color: #667eea;
    color: white;
}

/* الميزانية العمومية */
.balance-sheet-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.assets-section,
.liabilities-equity-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
}

.liabilities-section,
.equity-section {
    margin-bottom: 2rem;
}

.liabilities-section:last-child,
.equity-section:last-child {
    margin-bottom: 0;
}

/* التدفق النقدي */
.cash-flow-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-container {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    height: 400px;
}

.cash-flow-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cash-flow-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.cash-flow-item.total {
    background: #667eea;
    color: white;
}

.cash-flow-item h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
}

.cash-flow-item p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: bold;
}

/* تحسينات للتصميم المتجاوب للتقارير */
@media (max-width: 768px) {
    .report-actions {
        flex-direction: column;
    }

    .report-tabs {
        flex-direction: column;
    }

    .tab-btn {
        border-bottom: 1px solid #ddd;
        border-right: none;
    }

    .tab-btn.active {
        border-bottom-color: #ddd;
        border-right: 3px solid #667eea;
    }

    .date-inputs {
        flex-direction: column;
        align-items: stretch;
    }

    .balance-sheet-container {
        grid-template-columns: 1fr;
    }

    .cash-flow-container {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 300px;
    }
}

/* نافذة الإعدادات */
.settings-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.settings-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.settings-actions .btn {
    flex: 1;
    min-width: 150px;
}

.settings-section p {
    margin: 0.5rem 0;
    color: #666;
}

/* تحسينات إضافية */
.info-message {
    background-color: #d1ecf1;
    color: #0c5460;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border: 1px solid #bee5eb;
}

/* تحسين الأزرار */
.btn i {
    margin-left: 0.5rem;
}

/* تحسين الجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* تحسين النماذج */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* تحسين الرسائل */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    max-width: 400px;
}

.message {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* صفحة المساعدة */
.help-container {
    max-width: 800px;
    margin: 0 auto;
}

.help-container h1 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #667eea;
}

.help-section {
    background: white;
    margin-bottom: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
}

.help-section h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    margin: 0;
    font-size: 1.3rem;
}

.help-section h2 i {
    margin-left: 0.5rem;
}

.help-content {
    padding: 2rem;
}

.help-content ul {
    list-style: none;
    padding: 0;
}

.help-content li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.help-content li:last-child {
    border-bottom: none;
}

.help-content li strong {
    color: #667eea;
}

.faq-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.faq-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
}

kbd {
    background: #333;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9rem;
}

.help-footer {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-top: 2rem;
}

.help-footer p {
    margin-bottom: 1rem;
    color: #666;
}

/* تحسينات للتصميم المتجاوب للمساعدة */
@media (max-width: 768px) {
    .help-container {
        padding: 0 1rem;
    }

    .help-content {
        padding: 1rem;
    }

    .shortcuts-grid {
        grid-template-columns: 1fr;
    }

    .shortcut-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
