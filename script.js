// نظام العملات
const currencies = {
    'IQD': { name: 'الدينار العراقي', symbol: 'د.ع', code: 'IQD' },
    'USD': { name: 'الدولار الأمريكي', symbol: '$', code: 'USD' },
    'IRR': { name: 'التومان الإيراني', symbol: 'ت', code: 'IRR' },
    'SAR': { name: 'الريال السعودي', symbol: 'ر.س', code: 'SAR' }
};

// العملة الافتراضية
let selectedCurrency = localStorage.getItem('selectedCurrency') || 'IQD';

// بيانات التطبيق
let accounts = JSON.parse(localStorage.getItem('accounts')) || [
    { id: 1, name: 'النقدية', type: 'asset', balance: 10000 },
    { id: 2, name: 'البنك', type: 'asset', balance: 50000 },
    { id: 3, name: 'المبيعات', type: 'revenue', balance: 0 },
    { id: 4, name: 'المشتريات', type: 'expense', balance: 0 },
    { id: 5, name: 'الرواتب', type: 'expense', balance: 0 }
];

let transactions = JSON.parse(localStorage.getItem('transactions')) || [];
let invoices = JSON.parse(localStorage.getItem('invoices')) || [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    updateDashboard();

    // إعداد Electron إذا كان متاحاً
    if (window.isElectron) {
        setupElectronIntegration();
    }
});

// تهيئة التطبيق
function initializeApp() {
    // تحديث قوائم الحسابات في النماذج
    updateAccountSelects();

    // تحديث جدول آخر المعاملات
    updateRecentTransactions();

    // تحديث مؤشر العملة
    updateCurrencySelector();

    // إضافة تأثير التحميل
    document.body.classList.add('fade-in');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // شريط التنقل المتجاوب
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // نماذج الإدخال
    const newTransactionForm = document.getElementById('newTransactionForm');
    const newInvoiceForm = document.getElementById('newInvoiceForm');
    const newAccountForm = document.getElementById('newAccountForm');

    if (newTransactionForm) {
        newTransactionForm.addEventListener('submit', handleNewTransaction);
    }
    
    if (newInvoiceForm) {
        newInvoiceForm.addEventListener('submit', handleNewInvoice);
    }
    
    if (newAccountForm) {
        newAccountForm.addEventListener('submit', handleNewAccount);
    }

    // تعيين التاريخ الحالي
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });
}

// تحديث لوحة التحكم
function updateDashboard() {
    const totalRevenue = calculateTotalRevenue();
    const totalExpenses = calculateTotalExpenses();
    const netProfit = totalRevenue - totalExpenses;
    const totalInvoicesCount = invoices.length;

    // تحديث القيم في الواجهة
    updateElementText('totalRevenue', formatCurrency(totalRevenue));
    updateElementText('totalExpenses', formatCurrency(totalExpenses));
    updateElementText('netProfit', formatCurrency(netProfit));
    updateElementText('totalInvoices', totalInvoicesCount);

    // تغيير لون صافي الربح حسب القيمة
    const netProfitElement = document.getElementById('netProfit');
    if (netProfitElement) {
        netProfitElement.style.color = netProfit >= 0 ? '#28a745' : '#dc3545';
    }

    // تحديث إحصائيات المخزون إذا كانت متاحة
    updateInventoryDashboard();
}

// تحديث إحصائيات المخزون في لوحة التحكم
function updateInventoryDashboard() {
    // التحقق من وجود بيانات المخزون
    const inventoryItems = JSON.parse(localStorage.getItem('inventoryItems')) || [];

    if (inventoryItems.length > 0) {
        const inventoryValue = inventoryItems.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0);
        const lowStockCount = inventoryItems.filter(item => item.quantity <= item.minQuantity && item.quantity > 0).length;
        const outOfStockCount = inventoryItems.filter(item => item.quantity <= 0).length;

        // إضافة بطاقة إحصائيات المخزون إذا لم تكن موجودة
        addInventoryStatsCard(inventoryValue, lowStockCount, outOfStockCount);
    }
}

// إضافة بطاقة إحصائيات المخزون
function addInventoryStatsCard(inventoryValue, lowStockCount, outOfStockCount) {
    const statsGrid = document.querySelector('.stats-grid');
    if (!statsGrid) return;

    // التحقق من وجود البطاقة مسبقاً
    if (document.getElementById('inventoryValueCard')) {
        // تحديث القيم الموجودة
        updateElementText('inventoryValue', formatCurrency(inventoryValue));
        updateElementText('lowStockAlert', lowStockCount);
        return;
    }

    // إنشاء بطاقة جديدة
    const inventoryCard = document.createElement('div');
    inventoryCard.className = 'stat-card';
    inventoryCard.id = 'inventoryValueCard';
    inventoryCard.innerHTML = `
        <div class="stat-icon">
            <i class="fas fa-boxes"></i>
        </div>
        <div class="stat-info">
            <h3>قيمة المخزون</h3>
            <p class="stat-value" id="inventoryValue">${formatCurrency(inventoryValue)}</p>
        </div>
    `;

    // إضافة بطاقة تنبيهات المخزون
    const alertCard = document.createElement('div');
    alertCard.className = 'stat-card';
    alertCard.style.cursor = 'pointer';
    alertCard.onclick = () => window.location.href = 'inventory.html';
    alertCard.innerHTML = `
        <div class="stat-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-info">
            <h3>تنبيهات المخزون</h3>
            <p class="stat-value" id="lowStockAlert" style="color: ${lowStockCount > 0 ? '#dc3545' : '#28a745'}">${lowStockCount}</p>
        </div>
    `;

    statsGrid.appendChild(inventoryCard);
    statsGrid.appendChild(alertCard);
}

// حساب إجمالي الإيرادات
function calculateTotalRevenue() {
    return accounts
        .filter(account => account.type === 'revenue')
        .reduce((total, account) => total + account.balance, 0);
}

// حساب إجمالي المصروفات
function calculateTotalExpenses() {
    return accounts
        .filter(account => account.type === 'expense')
        .reduce((total, account) => total + account.balance, 0);
}

// تحديث نص العنصر
function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    const currency = currencies[selectedCurrency];
    if (!currency) return amount.toString();

    // تنسيق الرقم مع فواصل الآلاف
    const formattedNumber = new Intl.NumberFormat('ar', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);

    return `${formattedNumber} ${currency.symbol}`;
}

// تغيير العملة
function changeCurrency(currencyCode) {
    if (currencies[currencyCode]) {
        selectedCurrency = currencyCode;
        localStorage.setItem('selectedCurrency', currencyCode);

        // تحديث جميع العروض المالية
        updateDashboard();

        // تحديث مؤشر العملة المختارة
        updateCurrencySelector();

        showMessage(`تم تغيير العملة إلى ${currencies[currencyCode].name}`, 'success');
    }
}

// تحديث مؤشر العملة المختارة
function updateCurrencySelector() {
    const currencyButtons = document.querySelectorAll('.currency-btn');
    currencyButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.currency === selectedCurrency) {
            btn.classList.add('active');
        }
    });

    // تحديث نص العملة المختارة
    const currencyDisplay = document.getElementById('currentCurrency');
    if (currencyDisplay) {
        currencyDisplay.textContent = currencies[selectedCurrency].name;
    }

    // تحديث مؤشر العملة في شريط التنقل
    const navCurrency = document.getElementById('navCurrency');
    if (navCurrency) {
        navCurrency.textContent = currencies[selectedCurrency].symbol;
    }
}

// تحديث قوائم الحسابات
function updateAccountSelects() {
    const debitSelect = document.getElementById('debitAccount');
    const creditSelect = document.getElementById('creditAccount');
    
    if (debitSelect && creditSelect) {
        const accountOptions = accounts.map(account => 
            `<option value="${account.id}">${account.name}</option>`
        ).join('');
        
        debitSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
        creditSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
    }
}

// تحديث جدول آخر المعاملات
function updateRecentTransactions() {
    const tableBody = document.querySelector('#recentTransactionsTable tbody');
    if (!tableBody) return;

    const recentTransactions = transactions.slice(-5).reverse();
    
    if (recentTransactions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد معاملات</td></tr>';
        return;
    }

    tableBody.innerHTML = recentTransactions.map(transaction => {
        const debitAccount = accounts.find(acc => acc.id == transaction.debitAccountId);
        const creditAccount = accounts.find(acc => acc.id == transaction.creditAccountId);
        
        return `
            <tr>
                <td>${formatDate(transaction.date)}</td>
                <td>${transaction.description}</td>
                <td>${debitAccount ? debitAccount.name : 'غير محدد'}</td>
                <td>${creditAccount ? creditAccount.name : 'غير محدد'}</td>
                <td>${formatCurrency(transaction.amount)}</td>
            </tr>
        `;
    }).join('');
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// معالج القيد الجديد
function handleNewTransaction(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const transaction = {
        id: Date.now(),
        date: document.getElementById('transactionDate').value,
        description: document.getElementById('transactionDescription').value,
        debitAccountId: parseInt(document.getElementById('debitAccount').value),
        creditAccountId: parseInt(document.getElementById('creditAccount').value),
        amount: parseFloat(document.getElementById('transactionAmount').value)
    };

    // التحقق من صحة البيانات
    if (!transaction.debitAccountId || !transaction.creditAccountId) {
        showMessage('يرجى اختيار الحسابات المدين والدائن', 'error');
        return;
    }

    if (transaction.debitAccountId === transaction.creditAccountId) {
        showMessage('لا يمكن أن يكون الحساب المدين والدائن نفس الحساب', 'error');
        return;
    }

    // إضافة المعاملة
    transactions.push(transaction);
    
    // تحديث أرصدة الحسابات
    updateAccountBalance(transaction.debitAccountId, transaction.amount, 'debit');
    updateAccountBalance(transaction.creditAccountId, transaction.amount, 'credit');
    
    // حفظ البيانات
    saveData();
    
    // تحديث الواجهة
    updateDashboard();
    updateRecentTransactions();
    
    // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
    closeModal('newTransactionModal');
    event.target.reset();
    
    showMessage('تم إضافة القيد بنجاح', 'success');
}

// معالج الفاتورة الجديدة
function handleNewInvoice(event) {
    event.preventDefault();
    
    const invoice = {
        id: Date.now(),
        date: document.getElementById('invoiceDate').value,
        customerName: document.getElementById('customerName').value,
        description: document.getElementById('invoiceDescription').value,
        amount: parseFloat(document.getElementById('invoiceAmount').value)
    };

    // إضافة الفاتورة
    invoices.push(invoice);
    
    // إنشاء قيد محاسبي للفاتورة (مدين: العملاء، دائن: المبيعات)
    const salesAccount = accounts.find(acc => acc.name === 'المبيعات');
    const cashAccount = accounts.find(acc => acc.name === 'النقدية');
    
    if (salesAccount && cashAccount) {
        const transaction = {
            id: Date.now() + 1,
            date: invoice.date,
            description: `فاتورة مبيعات - ${invoice.customerName}`,
            debitAccountId: cashAccount.id,
            creditAccountId: salesAccount.id,
            amount: invoice.amount
        };
        
        transactions.push(transaction);
        updateAccountBalance(cashAccount.id, invoice.amount, 'debit');
        updateAccountBalance(salesAccount.id, invoice.amount, 'credit');
    }
    
    // حفظ البيانات
    saveData();
    
    // تحديث الواجهة
    updateDashboard();
    updateRecentTransactions();
    
    // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
    closeModal('newInvoiceModal');
    event.target.reset();
    
    showMessage('تم إضافة الفاتورة بنجاح', 'success');
}

// معالج الحساب الجديد
function handleNewAccount(event) {
    event.preventDefault();
    
    const account = {
        id: Date.now(),
        name: document.getElementById('accountName').value,
        type: document.getElementById('accountType').value,
        balance: parseFloat(document.getElementById('initialBalance').value) || 0
    };

    // التحقق من عدم تكرار اسم الحساب
    if (accounts.some(acc => acc.name === account.name)) {
        showMessage('اسم الحساب موجود بالفعل', 'error');
        return;
    }

    // إضافة الحساب
    accounts.push(account);
    
    // حفظ البيانات
    saveData();
    
    // تحديث قوائم الحسابات
    updateAccountSelects();
    
    // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
    closeModal('newAccountModal');
    event.target.reset();
    
    showMessage('تم إضافة الحساب بنجاح', 'success');
}

// تحديث رصيد الحساب
function updateAccountBalance(accountId, amount, type) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) return;

    if (type === 'debit') {
        if (account.type === 'asset' || account.type === 'expense') {
            account.balance += amount;
        } else {
            account.balance -= amount;
        }
    } else if (type === 'credit') {
        if (account.type === 'liability' || account.type === 'equity' || account.type === 'revenue') {
            account.balance += amount;
        } else {
            account.balance -= amount;
        }
    }
}

// فتح النافذة المنبثقة
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}

// عرض رسالة
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `${type}-message`;
    messageDiv.textContent = message;
    
    // إدراج الرسالة في أعلى المحتوى الرئيسي
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.insertBefore(messageDiv, mainContent.firstChild);
        
        // إزالة الرسالة بعد 3 ثوان
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
}

// حفظ البيانات في التخزين المحلي
function saveData() {
    localStorage.setItem('accounts', JSON.stringify(accounts));
    localStorage.setItem('transactions', JSON.stringify(transactions));
    localStorage.setItem('invoices', JSON.stringify(invoices));
}

// إنشاء تقرير سريع
function generateReport() {
    const reportData = {
        totalAccounts: accounts.length,
        totalTransactions: transactions.length,
        totalInvoices: invoices.length,
        totalRevenue: calculateTotalRevenue(),
        totalExpenses: calculateTotalExpenses(),
        netProfit: calculateTotalRevenue() - calculateTotalExpenses()
    };

    alert(`تقرير سريع:
    عدد الحسابات: ${reportData.totalAccounts}
    عدد المعاملات: ${reportData.totalTransactions}
    عدد الفواتير: ${reportData.totalInvoices}
    إجمالي الإيرادات: ${formatCurrency(reportData.totalRevenue)}
    إجمالي المصروفات: ${formatCurrency(reportData.totalExpenses)}
    صافي الربح: ${formatCurrency(reportData.netProfit)}`);
}

// تصدير البيانات
function exportData() {
    const data = {
        accounts: accounts,
        transactions: transactions,
        invoices: invoices,
        exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `accounting_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

// استيراد البيانات
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);

            if (data.accounts && data.transactions && data.invoices) {
                accounts = data.accounts;
                transactions = data.transactions;
                invoices = data.invoices;

                saveData();
                updateDashboard();
                updateRecentTransactions();
                updateAccountSelects();

                showMessage('تم استيراد البيانات بنجاح', 'success');
            } else {
                showMessage('ملف البيانات غير صحيح', 'error');
            }
        } catch (error) {
            showMessage('خطأ في قراءة الملف', 'error');
        }
    };
    reader.readAsText(file);
}

// مسح جميع البيانات
function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        localStorage.clear();
        location.reload();
    }
}

// التحقق من صحة البيانات
function validateData() {
    let errors = [];

    // التحقق من توازن القيود
    let totalDebit = 0;
    let totalCredit = 0;

    transactions.forEach(transaction => {
        totalDebit += transaction.amount;
        totalCredit += transaction.amount;
    });

    if (Math.abs(totalDebit - totalCredit) > 0.01) {
        errors.push('عدم توازن في القيود المحاسبية');
    }

    // التحقق من وجود حسابات مرتبطة بقيود محذوفة
    transactions.forEach(transaction => {
        const debitAccount = accounts.find(acc => acc.id === transaction.debitAccountId);
        const creditAccount = accounts.find(acc => acc.id === transaction.creditAccountId);

        if (!debitAccount) {
            errors.push(`حساب مدين مفقود في القيد رقم ${transaction.id}`);
        }
        if (!creditAccount) {
            errors.push(`حساب دائن مفقود في القيد رقم ${transaction.id}`);
        }
    });

    if (errors.length === 0) {
        showMessage('البيانات سليمة ولا توجد أخطاء', 'success');
    } else {
        showMessage(`تم العثور على ${errors.length} خطأ:\n${errors.join('\n')}`, 'error');
    }

    return errors.length === 0;
}

// وظائف Electron
function setupElectronIntegration() {
    // الاستماع لأحداث القائمة
    if (window.electronAPI) {
        window.electronAPI.onMenuAction((action, data) => {
            handleMenuAction(action, data);
        });
    }

    // تحديث وظائف التصدير والاستيراد
    updateElectronFunctions();

    // إضافة مؤشر أن التطبيق يعمل في Electron
    const electronIndicator = document.createElement('div');
    electronIndicator.className = 'electron-indicator';
    electronIndicator.innerHTML = '<i class="fas fa-desktop"></i> إصدار سطح المكتب';
    electronIndicator.style.cssText = `
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: rgba(102, 126, 234, 0.9);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 5px;
    `;
    document.body.appendChild(electronIndicator);
}

function handleMenuAction(action, data) {
    switch (action) {
        case 'new':
            if (confirm('هل تريد إنشاء ملف جديد؟ سيتم فقدان البيانات الحالية غير المحفوظة.')) {
                clearAllData();
            }
            break;
        case 'open':
            if (data) {
                loadDataFromFile(data);
            } else {
                importDataElectron();
            }
            break;
        case 'save':
            exportDataElectron();
            break;
        case 'save-as':
            if (data) {
                saveDataToFile(data);
            }
            break;
    }
}

function updateElectronFunctions() {
    // استبدال وظيفة التصدير
    window.exportDataElectron = exportDataElectron;
    window.importDataElectron = importDataElectron;
}

async function exportDataElectron() {
    if (!window.desktopUtils) return;

    const data = {
        accounts: accounts,
        transactions: transactions,
        invoices: invoices,
        inventoryItems: JSON.parse(localStorage.getItem('inventoryItems')) || [],
        stockMovements: JSON.parse(localStorage.getItem('stockMovements')) || [],
        selectedCurrency: selectedCurrency,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
    };

    const result = await window.desktopUtils.exportData(data);

    if (result.success) {
        showMessage('تم تصدير البيانات بنجاح إلى: ' + result.filePath, 'success');
    } else if (!result.canceled) {
        showMessage('فشل في تصدير البيانات: ' + (result.error || 'خطأ غير معروف'), 'error');
    }
}

async function importDataElectron() {
    if (!window.desktopUtils) return;

    const confirmed = await window.desktopUtils.showConfirmDialog(
        'هل تريد استيراد البيانات؟ سيتم استبدال البيانات الحالية.',
        'تأكيد الاستيراد'
    );

    if (!confirmed) return;

    const result = await window.desktopUtils.importData();

    if (result.success) {
        try {
            if (result.data.accounts) accounts = result.data.accounts;
            if (result.data.transactions) transactions = result.data.transactions;
            if (result.data.invoices) invoices = result.data.invoices;
            if (result.data.selectedCurrency) {
                selectedCurrency = result.data.selectedCurrency;
                localStorage.setItem('selectedCurrency', selectedCurrency);
            }
            if (result.data.inventoryItems) {
                localStorage.setItem('inventoryItems', JSON.stringify(result.data.inventoryItems));
            }
            if (result.data.stockMovements) {
                localStorage.setItem('stockMovements', JSON.stringify(result.data.stockMovements));
            }

            saveData();
            updateDashboard();
            updateRecentTransactions();
            updateAccountSelects();
            updateCurrencySelector();

            showMessage('تم استيراد البيانات بنجاح من: ' + result.filePath, 'success');

            // إعادة تحميل الصفحة لضمان التحديث الكامل
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            await window.desktopUtils.showErrorDialog('خطأ في معالجة البيانات: ' + error.message);
        }
    } else if (!result.canceled) {
        await window.desktopUtils.showErrorDialog('فشل في استيراد البيانات: ' + (result.error || 'خطأ غير معروف'));
    }
}

async function loadDataFromFile(filePath) {
    if (!window.electronAPI) return;

    const result = await window.electronAPI.loadFile(filePath);

    if (result.success) {
        try {
            const data = JSON.parse(result.data);

            if (data.accounts) accounts = data.accounts;
            if (data.transactions) transactions = data.transactions;
            if (data.invoices) invoices = data.invoices;
            if (data.selectedCurrency) {
                selectedCurrency = data.selectedCurrency;
                localStorage.setItem('selectedCurrency', selectedCurrency);
            }

            saveData();
            updateDashboard();
            updateRecentTransactions();
            updateAccountSelects();
            updateCurrencySelector();

            showMessage('تم تحميل البيانات بنجاح', 'success');

        } catch (error) {
            await window.desktopUtils.showErrorDialog('خطأ في قراءة الملف: ' + error.message);
        }
    } else {
        await window.desktopUtils.showErrorDialog('فشل في تحميل الملف: ' + result.error);
    }
}

async function saveDataToFile(filePath) {
    if (!window.electronAPI) return;

    const data = {
        accounts: accounts,
        transactions: transactions,
        invoices: invoices,
        inventoryItems: JSON.parse(localStorage.getItem('inventoryItems')) || [],
        stockMovements: JSON.parse(localStorage.getItem('stockMovements')) || [],
        selectedCurrency: selectedCurrency,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
    };

    const result = await window.electronAPI.saveFile(filePath, JSON.stringify(data, null, 2));

    if (result.success) {
        showMessage('تم حفظ البيانات بنجاح', 'success');
    } else {
        await window.desktopUtils.showErrorDialog('فشل في حفظ الملف: ' + result.error);
    }
}
