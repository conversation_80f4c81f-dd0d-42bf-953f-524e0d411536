# برنامج المحاسبة

برنامج محاسبة شامل مطور باستخدام HTML, CSS, و JavaScript لإدارة الحسابات المالية والقيود المحاسبية والفواتير والتقارير المالية.

## الميزات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- عرض الإحصائيات المالية الأساسية
- إجمالي الإيرادات والمصروفات
- صافي الربح/الخسارة
- عدد الفواتير
- الإجراءات السريعة
- آخر المعاملات
- مؤشر العملة المختارة

### 💱 نظام العملات المتعددة
- دعم أربع عملات رئيسية:
  - الدينار العراقي (د.ع)
  - الدولار الأمريكي ($)
  - التومان الإيراني (ت)
  - الريال السعودي (ر.س)
- تغيير العملة من الإعدادات
- مؤشر العملة في شريط التنقل
- تحديث فوري لجميع المبالغ المالية

### 📊 إدارة الحسابات
- إضافة وتعديل وحذف الحسابات
- تصنيف الحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- البحث والفلترة حسب نوع الحساب
- عرض أرصدة الحسابات
- إحصائيات الحسابات

### 📝 القيود المحاسبية
- إنشاء قيود محاسبية جديدة
- تعديل وحذف القيود
- البحث والفلترة حسب التاريخ
- ترقيم الصفحات
- إحصائيات القيود (إجمالي المدين والدائن)

### 🧾 إدارة الفواتير
- إنشاء فواتير جديدة
- تتبع حالة الفواتير (مدفوعة، معلقة، متأخرة)
- عرض وطباعة الفواتير
- إحصائيات المبيعات

### 📦 إدارة المواد والمخزون
- إضافة وتعديل وحذف المواد
- تصنيف المواد (منتجات، مواد خام، مستلزمات، معدات)
- تتبع الكميات والحد الأدنى للمخزون
- حركات المخزون (إدخال، إخراج، تسوية، نقل)
- تنبيهات المخزون المنخفض والمنتهي
- تقييم المخزون وحساب القيمة الإجمالية
- تقارير المخزون التفصيلية
- إدارة بيانات العملاء

### 📈 التقارير المالية
- **الميزانية العمومية**: عرض الأصول والخصوم وحقوق الملكية
- **قائمة الدخل**: عرض الإيرادات والمصروفات وصافي الربح
- **ميزان المراجعة**: عرض جميع الحسابات وأرصدتها
- **تقرير المخزون**: عرض تفصيلي لجميع المواد وقيمها وحالاتها
- **التدفق النقدي**: عرض التدفقات النقدية مع رسوم بيانية
- إمكانية طباعة وتصدير التقارير

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التنسيق والتصميم المتجاوب
- **JavaScript**: الوظائف والتفاعل
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات
- **Local Storage**: حفظ البيانات محلياً

## كيفية التشغيل

### الطريقة الأولى: خادم محلي بـ Python
```bash
# في مجلد المشروع
python -m http.server 8080
```
ثم افتح المتصفح على: `http://localhost:8080`

### الطريقة الثانية: خادم محلي بـ Node.js
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
http-server -p 8080
```

### الطريقة الثالثة: فتح مباشر
يمكن فتح ملف `index.html` مباشرة في المتصفح، لكن بعض الميزات قد لا تعمل بشكل صحيح.

## هيكل المشروع

```
برنامج-المحاسبة/
├── index.html          # الصفحة الرئيسية
├── accounts.html       # صفحة إدارة الحسابات
├── transactions.html   # صفحة القيود المحاسبية
├── invoices.html       # صفحة الفواتير
├── inventory.html      # صفحة إدارة المواد والمخزون
├── reports.html        # صفحة التقارير
├── help.html          # دليل الاستخدام
├── styles.css          # ملف التنسيقات
├── script.js           # ملف JavaScript الرئيسي
├── inventory.js        # ملف JavaScript للمخزون
├── manifest.json       # ملف تكوين التطبيق
├── package.json        # ملف تكوين المشروع
└── README.md          # ملف التوثيق
```

## الاستخدام

### إضافة حساب جديد
1. انتقل إلى صفحة "الحسابات"
2. اضغط على "إضافة حساب جديد"
3. املأ البيانات المطلوبة
4. اختر نوع الحساب
5. اضغط "حفظ"

### إنشاء قيد محاسبي
1. انتقل إلى صفحة "القيود" أو استخدم الإجراءات السريعة
2. اضغط على "قيد جديد"
3. اختر التاريخ والوصف
4. حدد الحساب المدين والدائن
5. أدخل المبلغ
6. اضغط "حفظ"

### إنشاء فاتورة
1. انتقل إلى صفحة "الفواتير"
2. اضغط على "فاتورة جديدة"
3. املأ بيانات العميل
4. أدخل تفاصيل الخدمة/المنتج
5. حدد المبلغ
6. اضغط "حفظ الفاتورة"

### إدارة المواد
1. انتقل إلى صفحة "المواد"
2. اضغط على "إضافة مادة جديدة"
3. أدخل كود المادة واسمها
4. اختر الفئة والوحدة
5. حدد الكمية الافتتاحية والحد الأدنى
6. أدخل أسعار الشراء والبيع
7. اضغط "حفظ المادة"

### حركة المخزون
1. من صفحة "المواد" اضغط "حركة مخزون"
2. اختر المادة ونوع الحركة
3. أدخل الكمية والسبب
4. اضغط "تنفيذ الحركة"

### تغيير العملة
1. اضغط على مؤشر العملة في شريط التنقل أو
2. انتقل إلى الإعدادات من الصفحة الرئيسية
3. اختر العملة المطلوبة من القائمة
4. سيتم تحديث جميع المبالغ فوراً

### عرض التقارير
1. انتقل إلى صفحة "التقارير"
2. اختر نوع التقرير من التبويبات
3. حدد نطاق التاريخ
4. اضغط "إنشاء التقرير"
5. يمكن طباعة أو تصدير التقرير

## الميزات المتقدمة

### التخزين المحلي
- جميع البيانات تُحفظ في متصفحك محلياً
- لا حاجة لقاعدة بيانات خارجية
- البيانات تبقى محفوظة حتى بعد إغلاق المتصفح

### التصميم المتجاوب
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- واجهة سهلة الاستخدام
- دعم اللغة العربية بالكامل

### الحسابات الافتراضية
يأتي البرنامج مع حسابات افتراضية:
- النقدية (أصول)
- البنك (أصول)
- المبيعات (إيرادات)
- المشتريات (مصروفات)
- الرواتب (مصروفات)
- المخزون (أصول) - يُضاف تلقائياً عند إضافة أول مادة

## التطوير المستقبلي

### ميزات مخططة
- [ ] تصدير البيانات إلى Excel/PDF
- [ ] نسخ احتياطي واستيراد البيانات
- [ ] تقارير مالية متقدمة
- [ ] إدارة المخزون
- [ ] نظام المستخدمين والصلاحيات
- [ ] ربط مع البنوك
- [ ] تطبيق موبايل

### المساهمة
نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- ترجمة البرنامج

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:
1. تأكد من تشغيل البرنامج على خادم محلي
2. تأكد من تفعيل JavaScript في المتصفح
3. استخدم متصفح حديث (Chrome, Firefox, Safari, Edge)

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**ملاحظة**: هذا البرنامج مصمم للاستخدام التعليمي والشركات الصغيرة. للاستخدام التجاري الكبير، يُنصح بإضافة ميزات أمان إضافية وقاعدة بيانات خارجية.
